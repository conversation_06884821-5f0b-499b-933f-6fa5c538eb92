package com.hbg.lib.widgets

import android.content.Context
import android.util.AttributeSet
import android.widget.RelativeLayout

/**
 * 带加载状态的RelativeLayout
 */
class LoadingRelativeLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    // 可以在这里添加加载状态的逻辑
    // 目前保持与标准RelativeLayout相同的行为
    
}
