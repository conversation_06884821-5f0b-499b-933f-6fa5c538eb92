package com.huobi.home.ui

import android.Manifest
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.annotation.Keep
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.business.common.red_packet.RedPacketManager
import com.google.android.material.appbar.AppBarLayout
import com.google.zxing.client.android.CaptureActivity
import com.hbg.lib.common.utils.DateTimeUtils
import com.hbg.lib.common.utils.DebugLog
import com.hbg.lib.common.utils.HUIHandler
import com.hbg.lib.common.utils.SP
import com.hbg.lib.common.utils.crypt.MD5Utils
import com.hbg.lib.core.BaseModuleConfig
import com.hbg.lib.core.constants.ConfigConstant
import com.hbg.lib.core.model.ContractOverviewController
import com.hbg.lib.core.network.rx.EasySubscriber
import com.hbg.lib.core.page.SmartRefreshFooter
import com.hbg.lib.core.permissions.EasyPermissions
import com.hbg.lib.core.permissions.EasyPermissions.PermissionCallbacks
import com.hbg.lib.core.ui.BaseActivity
import com.hbg.lib.core.util.AppLanguageHelper
import com.hbg.lib.core.util.NightHelper
import com.hbg.lib.core.util.PhoneUtils
import com.hbg.lib.core.util.RxJavaHelper
import com.hbg.lib.core.util.ThemeHelper
import com.hbg.lib.core.webview.HBBaseWebActivity
import com.hbg.lib.network.hbg.HbgGlobalApi
import com.hbg.lib.network.hbg.core.bean.TokenBindInfo
import com.hbg.lib.network.linear.swap.controller.LinearSwapOverviewController
import com.hbg.lib.network.pro.HbgProApi
import com.hbg.lib.network.pro.core.bean.ProTokenUpdate
import com.hbg.lib.network.pro.socket.listener.LastKlineListener
import com.hbg.lib.network.pro.socket.listener.MarketOverviewListenerV2
import com.hbg.lib.network.pro.socket.response.LastKlineResponse
import com.hbg.lib.network.pro.socket.response.MarketOverviewV2Response
import com.hbg.lib.network.retrofit.exception.APIStatusErrorException
import com.hbg.lib.network.swap.core.controller.SwapOverviewController
import com.hbg.lib.widgets.utils.HuobiToastUtil
import com.hbg.module.content.ui.fragment.NewsChildFragment
import com.hbg.module.huobi.im.RedPoint.RedPointHelper
import com.hbg.module.libkt.base.adapter.BasePageAdapter
import com.hbg.module.libkt.base.ext.toGsonStr
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.indicator.IndicatorHelper
import com.hbg.module.libkt.custom.indicator.TabClickListener
import com.hbg.module.libkt.custom.indicator.TabData
import com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator
import com.hbg.module.libkt.custom.indicator.navigator.titles.RedPointPagerTitleView
import com.hbg.module.libkt.utils.event.LiveDataBus.with
import com.hbg.module.libkt.utils.event.LiveKeyCons
import com.hbg.module.livesquare.ui.LiveSquareHomeFragment
import com.hbg.module.livesquare.utils.LiveTrackUtils
import com.huobi.R
import com.huobi.account.event.LogOutEvent
import com.huobi.account.helper.UserProvider
import com.huobi.account.ui.SecuritySetActivity
import com.huobi.account.ui.VerificationStartActivity
import com.huobi.apm.AppStartMonitorManager
import com.huobi.edgeengine.EdgeEngine
import com.huobi.edgeengine.node.DataCallback
import com.huobi.finance.api.RiskService
import com.huobi.finance.bean.TsvMsg
import com.huobi.home.data.HomeContentTabRedPointEvent
import com.huobi.home.data.HomepageConfig
import com.huobi.home.data.TransferAmountInfo
import com.huobi.home.engine.HomeBridgeAbility
import com.huobi.home.engine.HomeEngineCore
import com.huobi.home.engine.HomeEngineEvent
import com.huobi.home.presenter.HomePresenter
import com.huobi.homemarket.model.ProOverviewController
import com.huobi.index.bean.IndexInformationRequestData
import com.huobi.index.bean.IndexInformationRequestData.ACTION_REFRESH
import com.huobi.index.bean.IndexInformationRequestData.ACTION_UP
import com.huobi.index.helper.IndexHelper
import com.huobi.index.helper.LoginTokenHelper
import com.huobi.index.trace.IndexLifeCycleStep
import com.huobi.index.trace.IndexLifeCycleTracer
import com.huobi.index.ui.AnnouncementFragment
import com.huobi.index.ui.FeedFragment
import com.huobi.index.ui.ScanLoginSuccessActivity
import com.huobi.login.bean.IAuthTarget
import com.huobi.login.usercenter.external.UserCenterActionHelper
import com.huobi.main.helper.HBHomeSkinHelper
import com.huobi.main.navigator.JumpUtils
import com.huobi.page.SmartRefreshHeader
import com.huobi.retrofit.HRetrofit
import com.huobi.statistics.SensorsDataHelper
import com.huobi.utils.ScanUtils
import com.huobi.view.MyNestedScrollView
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.constant.RefreshState
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.scwang.smartrefresh.layout.listener.SimpleMultiPurposeListener
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.Serializable
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 *@Description:
 *@Author: wangyuxi
 *@Date: 2023/4/13
 */
class HomeFragment : BaseHomeFragment<HomePresenter, HomePresenter.HomeUI>(), HomePresenter.HomeUI, PermissionCallbacks {

    val TAG = "HomeFragment"

    // webview pool container
    private var mWebviewPoolContainer: FrameLayout? = null

    //navigation 区域
    private var mNavigation: RelativeLayout? = null
    private var mNavigationContainer: LinearLayout? = null

    //下拉刷新
    private var mRefreshLayout: SmartRefreshLayout? = null

    //下拉刷新头
    private var mCustomHeader: SmartRefreshHeader? = null

    //滚动组件
    private var mFluentScrollView: MyNestedScrollView? = null
    private var clLayout: CoordinatorLayout? = null
    private var appBarLayout: AppBarLayout? = null

    //    private var mFluentScrollerLayout: ConsecutiveScrollerLayout? = null
    private var mFluentLayout: LinearLayout? = null

    //内容feed
    private var coIndicator: CoIndicator? = null
    private var mFeedViewPager: ViewPager2? = null
    private var mFeedTabLayoutGroup: LinearLayout? = null
    private var mFeedReleaseBtn: View? = null
    private var feedFragmentList: ArrayList<Fragment>? = null

    //行情数据监听
    private var mProListener: MarketOverviewListenerV2? = null

    //端引擎
    private var mEdgeEngine: EdgeEngine? = null

    private var homepageConfig: HomepageConfig? = null
    private var transferAmountInfo: TransferAmountInfo? = null

    private var homeAnimation: ViewGroup? = null

    private var isTopIcon = false
    private var homeRadioContainer: ViewGroup? = null
    private var homeCheckBox: CheckBox? = null
    private var homeCheckBoxBg: CheckBox? = null
    private var homeCheckBoxIcon: CheckBox? = null
    private var animator: ValueAnimator? = null
    private var mScrollY = 0
    private var mIsGetProToken = false
    private var feedScroll = false
    private var lastScrollUpdate: Long = -1

    private var nowCurrentContentPos = -1
    var tabs: ArrayList<TabData>? = null
    private var stickyBottomTabPlugin: StickyBottomTabPlugin? = null

    /**
     * 检测外部Activity的底部导航栏高度
     */
    private fun detectBottomNavigationHeight(): Int {
        return try {
            val activity = requireActivity()
            android.util.Log.d("HomeFragment", "检测外部Activity导航栏 (${activity::class.java.simpleName})")

            // 直接检测main_tab导航栏
            val navigationView = activity.findViewById<View>(R.id.main_tab)
            if (navigationView != null) {
                val height = navigationView.height
                val location = IntArray(2)
                navigationView.getLocationOnScreen(location)

                android.util.Log.d("HomeFragment", "main_tab: 高度=${height}px, 位置=y${location[1]}px, 类型=${navigationView::class.java.simpleName}")

                if (height > 0) {
                    android.util.Log.i("HomeFragment", " 检测到导航栏高度: ${height}px")
                    return height
                } else {
                    android.util.Log.w("HomeFragment", " 导航栏高度为0，布局可能未完成")
                }
            } else {
                android.util.Log.w("HomeFragment", " 未找到main_tab导航栏")
            }

            // 如果检测失败，返回标准高度
            val standardHeight = (56 * requireContext().resources.displayMetrics.density).toInt()
            android.util.Log.w("HomeFragment", "使用标准高度: ${standardHeight}px")
            return standardHeight

        } catch (e: Exception) {
            android.util.Log.e("HomeFragment", "检测导航栏失败", e)
            (56 * requireContext().resources.displayMetrics.density).toInt()
        }
    }



    /**
     * 重新配置插件的导航栏适配
     * 在Fragment可见时调用，确保获取到正确的导航栏高度
     */
    private fun reconfigureNavigationBarAdaptation() {
        if (stickyBottomTabPlugin == null) {
            android.util.Log.w("HomeFragment", "插件不存在，无法重新配置导航栏适配")
            return
        }

        try {
            val currentNavHeight = detectBottomNavigationHeight()
            android.util.Log.d("HomeFragment", "=== 重新配置导航栏适配 ===")
            android.util.Log.d("HomeFragment", "当前检测到的导航栏高度: ${currentNavHeight}px")

            if (currentNavHeight > 0) {
                android.util.Log.d("HomeFragment", "检测到有效导航栏高度，重新创建插件...")

                // 先禁用当前插件
                stickyBottomTabPlugin?.disable()

                // 获取当前配置
                val tabTitles =  listOf(
                    resources.getString(R.string.n_content_found),
                    resources.getString(R.string.n_content_communityList_attention),
                    resources.getString(R.string.n_content_newsflash),
                    resources.getString(R.string.n_live),
                    resources.getString(R.string.n_notice)
                )

                // 重新创建插件，使用正确的导航栏高度
                stickyBottomTabPlugin = StickyBottomTabPluginFactory.createForCoIndicator(
                    context = requireContext(),
                    coordinatorLayout = clLayout!!,
                    appBarLayout = appBarLayout!!,
                    coIndicator = coIndicator!!,
                    viewPager = mFeedViewPager!!,
                    tabTitles = tabTitles,
                    animationDuration = 300L,
                    scrollToPosition = 0.33f,
                    autoDetectNavigationBar = true,
                    bottomMargin = currentNavHeight,  // 使用正确的导航栏高度
                    maxElevation = 20f
                )

                // 启用新插件
                stickyBottomTabPlugin?.enable()

                android.util.Log.i("HomeFragment", " 插件重新配置完成，导航栏适配已更新")
            } else {
                android.util.Log.d("HomeFragment", "导航栏高度仍为0，保持当前配置")
            }

            android.util.Log.d("HomeFragment", "=== 重新配置完成 ===")
        } catch (e: Exception) {
            android.util.Log.e("HomeFragment", "重新配置导航栏适配失败", e)
        }
    }

    /**
     * 验证主应用导航栏适配效果
     */
    private fun verifyNavigationBarAdaptation() {
        try {
            val activity = requireActivity()
            val screenHeight = activity.resources.displayMetrics.heightPixels
            val bottomNavHeight = detectBottomNavigationHeight()

            android.util.Log.d("HomeFragment", "=== 导航栏适配验证 ===")
            android.util.Log.d("HomeFragment", "屏幕高度: ${screenHeight}px")
            android.util.Log.d("HomeFragment", "底部导航栏高度: ${bottomNavHeight}px")
            android.util.Log.d("HomeFragment", "可用内容高度: ${screenHeight - bottomNavHeight}px")

            // 检查外部Activity导航栏是否存在 (main_tab)
            val navigation = activity.findViewById<View>(R.id.main_tab)
            if (navigation != null) {
                val location = IntArray(2)
                navigation.getLocationOnScreen(location)
                android.util.Log.d("HomeFragment", "验证外部Activity导航栏 (main_tab):")
                android.util.Log.d("HomeFragment", "  - 位置: y=${location[1]}px, 高度=${navigation.height}px")
                android.util.Log.d("HomeFragment", "  - 可见性: ${if (navigation.visibility == View.VISIBLE) "可见" else "隐藏"}")
            } else {
                android.util.Log.w("HomeFragment", " 验证时未找到外部Activity导航栏 (main_tab)")
            }

            android.util.Log.d("HomeFragment", "=== 验证完成 ===")
        } catch (e: Exception) {
            android.util.Log.e("HomeFragment", "导航栏适配验证失败", e)
        }
    }

    /**
     * 调试方法：打印插件状态信息
     */
    private fun debugPluginStatus(action: String) {
        android.util.Log.d("HomeFragment", "=== 插件状态调试 [$action] ===")
        android.util.Log.d("HomeFragment", "插件实例: ${if (stickyBottomTabPlugin != null) "存在" else "不存在"}")
        if (stickyBottomTabPlugin != null) {
            android.util.Log.d("HomeFragment", "插件启用状态: ${stickyBottomTabPlugin?.isEnabled()}")
            android.util.Log.d("HomeFragment", "吸底Tab可见: ${stickyBottomTabPlugin?.isBottomTabVisible()}")
        }
        android.util.Log.d("HomeFragment", "Fragment可见: ${ui?.isCanBeSeen}")
        android.util.Log.d("HomeFragment", "ViewPager当前页: ${mFeedViewPager?.currentItem}")

        // 添加导航栏适配状态
        val bottomNavHeight = detectBottomNavigationHeight()
        android.util.Log.d("HomeFragment", "当前检测导航栏高度: ${bottomNavHeight}px")

        // 添加详细的Tab组件调试信息
        debugTabComponentDetails()

        // 添加滚动状态调试信息
        debugScrollState()

        android.util.Log.d("HomeFragment", "=== 状态调试结束 ===")
    }

    /**
     * 调试Tab组件详细信息
     */
    private fun debugTabComponentDetails() {
        android.util.Log.d("HomeFragment", "--- Tab组件详细信息 ---")

        // CoIndicator详细信息
        if (coIndicator != null) {
            val location = IntArray(2)
            coIndicator!!.getLocationOnScreen(location)

            android.util.Log.d("HomeFragment", "CoIndicator状态:")
            android.util.Log.d("HomeFragment", "  - 位置: x=${location[0]}px, y=${location[1]}px")
            android.util.Log.d("HomeFragment", "  - 尺寸: 宽=${coIndicator!!.width}px, 高=${coIndicator!!.height}px")
            android.util.Log.d("HomeFragment", "  - 可见性: ${if (coIndicator!!.visibility == View.VISIBLE) "可见" else "隐藏"}")
            android.util.Log.d("HomeFragment", "  - 子View数量: ${coIndicator!!.childCount}")

            // 检查Tab是否在屏幕可见区域内
            val screenHeight = requireActivity().resources.displayMetrics.heightPixels
            val tabBottom = location[1] + coIndicator!!.height
            val isTabVisible = location[1] >= 0 && tabBottom <= screenHeight
            android.util.Log.d("HomeFragment", "  - 屏幕内可见: $isTabVisible (屏幕高度: ${screenHeight}px)")

            // 输出Tab标题信息
            tabs?.forEachIndexed { index, tabData ->
                android.util.Log.d("HomeFragment", "  - Tab[$index]: ${tabData.name}")
            }
        } else {
            android.util.Log.w("HomeFragment", "CoIndicator为空!")
        }

        // ViewPager详细信息
        if (mFeedViewPager != null) {
            android.util.Log.d("HomeFragment", "ViewPager状态:")
            android.util.Log.d("HomeFragment", "  - 当前页面: ${mFeedViewPager!!.currentItem}")
            android.util.Log.d("HomeFragment", "  - 总页面数: ${mFeedViewPager!!.adapter?.itemCount ?: 0}")
            android.util.Log.d("HomeFragment", "  - 滚动状态: ${getScrollStateString(mFeedViewPager!!.scrollState)}")
            android.util.Log.d("HomeFragment", "  - 用户输入启用: ${mFeedViewPager!!.isUserInputEnabled}")
        } else {
            android.util.Log.w("HomeFragment", "ViewPager为空!")
        }
    }

    /**
     * 调试滚动状态信息
     */
    private fun debugScrollState() {
        android.util.Log.d("HomeFragment", "--- 滚动状态信息 ---")

        // AppBarLayout滚动状态
        if (appBarLayout != null) {
            val totalScrollRange = appBarLayout!!.totalScrollRange
            android.util.Log.d("HomeFragment", "AppBarLayout状态:")
            android.util.Log.d("HomeFragment", "  - 总滚动范围: ${totalScrollRange}px")
            android.util.Log.d("HomeFragment", "  - 子View数量: ${appBarLayout!!.childCount}")

            // 检查AppBarLayout的滚动监听器
            try {
                val field = appBarLayout!!.javaClass.getDeclaredField("mListeners")
                field.isAccessible = true
                val listeners = field.get(appBarLayout!!) as? List<*>
                android.util.Log.d("HomeFragment", "  - 滚动监听器数量: ${listeners?.size ?: 0}")
            } catch (e: Exception) {
                android.util.Log.d("HomeFragment", "  - 无法获取滚动监听器信息: ${e.message}")
            }
        } else {
            android.util.Log.w("HomeFragment", "AppBarLayout为空!")
        }

        // CoordinatorLayout状态
        if (clLayout != null) {
            android.util.Log.d("HomeFragment", "CoordinatorLayout状态:")
            android.util.Log.d("HomeFragment", "  - 子View数量: ${clLayout!!.childCount}")
            android.util.Log.d("HomeFragment", "  - 尺寸: 宽=${clLayout!!.width}px, 高=${clLayout!!.height}px")
        } else {
            android.util.Log.w("HomeFragment", "CoordinatorLayout为空!")
        }
    }

    /**
     * 获取ViewPager滚动状态字符串
     */
    private fun getScrollStateString(scrollState: Int): String {
        return when (scrollState) {
            ViewPager2.SCROLL_STATE_IDLE -> "IDLE(空闲)"
            ViewPager2.SCROLL_STATE_DRAGGING -> "DRAGGING(拖拽中)"
            ViewPager2.SCROLL_STATE_SETTLING -> "SETTLING(自动滚动中)"
            else -> "UNKNOWN($scrollState)"
        }
    }

    // 调试用的滚动监听器
    private var debugScrollListener: AppBarLayout.OnOffsetChangedListener? = null

    /**
     * 添加调试滚动监听器
     */
    private fun addDebugScrollListener() {
        if (appBarLayout == null) {
            android.util.Log.w("HomeFragment", "AppBarLayout为空，无法添加调试滚动监听器")
            return
        }

        // 移除之前的监听器
        removeDebugScrollListener()

        debugScrollListener = AppBarLayout.OnOffsetChangedListener { appBarLayout, verticalOffset ->
            android.util.Log.d("HomeFragment", "=== 滚动事件调试 ===")
            android.util.Log.d("HomeFragment", "AppBarLayout滚动偏移: ${verticalOffset}px")
            android.util.Log.d("HomeFragment", "总滚动范围: ${appBarLayout.totalScrollRange}px")
            android.util.Log.d("HomeFragment", "滚动百分比: ${Math.abs(verticalOffset).toFloat() / appBarLayout.totalScrollRange * 100}%")

            // 检查CoIndicator的实时位置和可见性
            if (coIndicator != null) {
                val location = IntArray(2)
                coIndicator!!.getLocationOnScreen(location)
                val screenHeight = requireActivity().resources.displayMetrics.heightPixels
                val tabBottom = location[1] + coIndicator!!.height
                val isTabVisible = location[1] >= 0 && tabBottom <= screenHeight

                android.util.Log.d("HomeFragment", "CoIndicator实时状态:")
                android.util.Log.d("HomeFragment", "  - 屏幕位置: y=${location[1]}px")
                android.util.Log.d("HomeFragment", "  - 底部位置: y=${tabBottom}px")
                android.util.Log.d("HomeFragment", "  - 屏幕内可见: $isTabVisible")
                android.util.Log.d("HomeFragment", "  - 高度: ${coIndicator!!.height}px")

                // 检查是否应该显示吸底Tab
                val shouldShowBottomTab = !isTabVisible
                android.util.Log.d("HomeFragment", "  - 应该显示吸底Tab: $shouldShowBottomTab")

                // 检查插件状态
                if (stickyBottomTabPlugin != null) {
                    android.util.Log.d("HomeFragment", "插件当前状态:")
                    android.util.Log.d("HomeFragment", "  - 插件启用: ${stickyBottomTabPlugin?.isEnabled()}")
                    android.util.Log.d("HomeFragment", "  - 吸底Tab可见: ${stickyBottomTabPlugin?.isBottomTabVisible()}")
                } else {
                    android.util.Log.w("HomeFragment", "插件实例为空!")
                }
            }

            android.util.Log.d("HomeFragment", "=== 滚动事件调试结束 ===")
        }

        appBarLayout!!.addOnOffsetChangedListener(debugScrollListener)
        android.util.Log.i("HomeFragment", " 调试滚动监听器已添加")
    }

    /**
     * 移除调试滚动监听器
     */
    private fun removeDebugScrollListener() {
        if (debugScrollListener != null && appBarLayout != null) {
            appBarLayout!!.removeOnOffsetChangedListener(debugScrollListener)
            debugScrollListener = null
            android.util.Log.d("HomeFragment", "调试滚动监听器已移除")
        }
    }

    override fun createView(inflater: LayoutInflater, container: ViewGroup, savedInstanceState: Bundle?): View {
        val layoutRes: Int = getAppropriateLayoutResId()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        //啄木鸟首页启动埋点
        IndexLifeCycleTracer.getInstance().report(IndexLifeCycleStep.AppHomePage)
        HomeEngineCore.navModuleViews.clear()
        HomeEngineCore.fluentModuleViews.clear()
        return ThemeHelper.instance.getNewThemeView(requireActivity(), inflater, layoutRes, container)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        setRootViewOnly(null)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        presenter.getHomePageConfig()
        presenter.refreshContract()
    }

    override fun getUI(): HomePresenter.HomeUI {
        return this
    }

    override fun createPresenter(): HomePresenter {
        return HomePresenter()
    }



    private fun setupStickyBottomTabPlugin() {
        android.util.Log.d("HomeFragment", "=== 开始初始化吸底Tab插件 ===")

        // 详细的组件状态检查
        android.util.Log.d("HomeFragment", "组件状态检查:")
        android.util.Log.d("HomeFragment", "  - clLayout: ${if (clLayout != null) "✓ 已初始化" else "✗ 未初始化"}")
        android.util.Log.d("HomeFragment", "  - appBarLayout: ${if (appBarLayout != null) "✓ 已初始化" else "✗ 未初始化"}")
        android.util.Log.d("HomeFragment", "  - coIndicator: ${if (coIndicator != null) "✓ 已初始化" else "✗ 未初始化"}")
        android.util.Log.d("HomeFragment", "  - mFeedViewPager: ${if (mFeedViewPager != null) "✓ 已初始化" else "✗ 未初始化"}")
        android.util.Log.d("HomeFragment", "  - tabs: ${if (tabs != null) "✓ 已初始化(${tabs?.size}个)" else "✗ 未初始化"}")

        // 确保必要的组件已初始化
        if (clLayout == null || appBarLayout == null || coIndicator == null || mFeedViewPager == null) {
            android.util.Log.e("HomeFragment", "插件初始化失败：必要组件未初始化")
            return
        }

        // 从tabs数据中获取实际的标题
        val tabTitles = tabs?.map { it.name } ?: listOf(
            resources.getString(R.string.n_content_found),
            resources.getString(R.string.n_content_communityList_attention),
            resources.getString(R.string.n_content_newsflash),
            resources.getString(R.string.n_live),
            resources.getString(R.string.n_notice)
        )

        android.util.Log.d("HomeFragment", "Tab标题列表: $tabTitles")
        android.util.Log.d("HomeFragment", "ViewPager当前页面: ${mFeedViewPager?.currentItem}")

        try {
            android.util.Log.d("HomeFragment", "正在创建插件实例...")

            // 检测主应用导航栏高度
            val bottomNavigationHeight = detectBottomNavigationHeight()
            android.util.Log.d("HomeFragment", "检测到主应用导航栏高度: ${bottomNavigationHeight}px")

            // 使用工厂方法创建插件，针对主应用导航栏进行优化配置
            stickyBottomTabPlugin = StickyBottomTabPluginFactory.createForCoIndicator(
                context = requireContext(),
                coordinatorLayout = clLayout!!,
                appBarLayout = appBarLayout!!,
                coIndicator = coIndicator!!,
                viewPager = mFeedViewPager!!,
                tabTitles = tabTitles,
                animationDuration = 300L,        // 动画时长
                scrollToPosition = 0.33f,        // 点击后滚动到屏幕1/3位置
                autoDetectNavigationBar = true,  // 启用导航栏自动检测
                bottomMargin = bottomNavigationHeight,  // 设置底部边距避开主导航栏
                maxElevation = 20f              // 提高elevation确保在导航栏之上
            )

            android.util.Log.d("HomeFragment", "插件实例创建成功，正在启用...")
            stickyBottomTabPlugin?.enable()

            android.util.Log.i("HomeFragment", " 吸底Tab插件启用成功！")
            android.util.Log.d("HomeFragment", "插件配置信息:")
            android.util.Log.d("HomeFragment", "  - 动画时长: 300ms")
            android.util.Log.d("HomeFragment", "  - 滚动位置: 33%")
            android.util.Log.d("HomeFragment", "  - 自动检测导航栏: 启用")
            android.util.Log.d("HomeFragment", "  - 底部边距: ${bottomNavigationHeight}px")
            android.util.Log.d("HomeFragment", "  - 最大elevation: 20f")
            android.util.Log.d("HomeFragment", "  - 主应用导航栏适配: 启用")
            android.util.Log.d("HomeFragment", "=== 插件初始化完成 ===")

            // 验证导航栏适配效果
            verifyNavigationBarAdaptation()

            // 添加调试滚动监听器
            addDebugScrollListener()

            // 调试插件状态
            debugPluginStatus("初始化完成")

        } catch (e: Exception) {
            android.util.Log.e("HomeFragment", " 吸底Tab插件启用失败", e)
            android.util.Log.e("HomeFragment", "错误详情: ${e.message}")
            e.printStackTrace()
        }
    }
    @SuppressLint("RestrictedApi")
    override fun initViews() {
        mWebviewPoolContainer = viewFinder.find(R.id.webview_pool_container)

        mNavigation = viewFinder.find(R.id.navigation_container)
        mNavigationContainer = viewFinder.find(R.id.home_navigation_ll)
        val statusBar = viewFinder.find<View>(R.id.home_status_bar)
        val params = statusBar.layoutParams
        params.height = BaseActivity.getStatusBarHeight(statusBar.context)
        statusBar.layoutParams = params

        mRefreshLayout = viewFinder.find(R.id.fluent_refresh_layout)
        clLayout = viewFinder.find(R.id.clLayout)
        appBarLayout = viewFinder.find(R.id.appBarLayout)
//        mFluentScrollerLayout = viewFinder.find(R.id.fluent_scrollerlayout)
        mFluentScrollView = viewFinder.find(R.id.fluent_content_nsv)
        mFluentLayout = viewFinder.find(R.id.fluent_container)

        homeAnimation = viewFinder.find(R.id.rl_new_hand_area_animation_layer)

        initEngine()
        initViewPager()
        initWifiInfo(activity)
        setRefreshLayout()

        // 插件初始化移到initViewPager()之后，确保tabs数据已准备好
        // setupStickyBottomTabPlugin() 现在在initViewPager()中调用

        mRefreshLayout?.setOnMultiPurposeListener(object : SimpleMultiPurposeListener() {
//            override fun onFooterReleasing(footer: RefreshFooter, percent: Float, offset: Int, footerHeight: Int, extendHeight: Int) {
//            }
//
//            override fun onFooterPulling(footer: RefreshFooter, percent: Float, offset: Int, footerHeight: Int, extendHeight: Int) {
//                mFluentScrollerLayout?.stickyOffset = offset
//            }
        })

        activity?.let {
            homeRadioContainer = it.findViewById(R.id.main_home_tab)
            homeRadioContainer?.run {
                homeCheckBox = this.findViewById(R.id.main_index_cb)
                homeCheckBoxBg = this.findViewById(R.id.main_index_cb_bg)
                homeCheckBoxIcon = this.findViewById(R.id.main_index_cb_icon)
                val remoteSkinBean = HBHomeSkinHelper.getInstance().getRealRemoteSkinBean(it)
                val isNightMode = NightHelper.getInstance().isNightMode
                remoteSkinBean?.let { bean ->
                    bean.tabbar?.let { bar ->
                        val tabBarBean = if (isNightMode) {
                            bar.night
                        } else {
                            bar.light
                        }
                        tabBarBean?.let {
                            val homeIcon = tabBarBean.home.icon
                            val rocketIcon = tabBarBean.rocket.rocket_icon
                            val rocketBg = tabBarBean.rocket.rocket_bg
                            homeCheckBoxBg?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_bg, rocketBg, homeIcon)
                            homeCheckBoxIcon?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_icon, rocketIcon, homeIcon)
                        }
                    }
                }
            }
        }

        AppStartMonitorManager.getInstance().mainAppear();
    }

    fun checkFutureSub() {
//        目前首页没用到合约的推送 取消订阅 解决安卓首页卡顿，闪退问题
//        val canBeSeen = ui.isCanBeSeen
//        if (canBeSeen) {
//            subscribeFutureMarketWebSocket()
//        } else {
//            unsubscribeFutureMarketWebSocket()
//        }
    }

    /**
     * 合约行情ws订阅
     */
    private fun subscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "subscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().subOverview()
        SwapOverviewController.getInstance().subOverview()
        LinearSwapOverviewController.getInstance().subOverview()
    }

    /**
     * 合约行情ws退订
     */
    private fun unsubscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "unsubscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().unSubOverView()
        SwapOverviewController.getInstance().unSubOverView()
        LinearSwapOverviewController.getInstance().unSubOverView()
    }

    override fun addEvent() {
//        registMarketListener()
        registLogin()
        homeIconEvent()
    }


    override fun afterInit() {
    }


    override fun onDestroyView() {
        android.util.Log.d("HomeFragment", "=== onDestroyView: 开始清理资源 ===")

        // 移除调试滚动监听器
        removeDebugScrollListener()

        // 销毁插件，释放资源
        if (stickyBottomTabPlugin != null) {
            android.util.Log.d("HomeFragment", "正在销毁吸底Tab插件...")
            stickyBottomTabPlugin?.destroy()
            stickyBottomTabPlugin = null
            android.util.Log.i("HomeFragment", " 吸底Tab插件已销毁")
        } else {
            android.util.Log.d("HomeFragment", "吸底Tab插件为空，无需销毁")
        }

        mFeedViewPager?.adapter = null
        android.util.Log.d("HomeFragment", "=== onDestroyView: 资源清理完成 ===")
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        if (mEdgeEngine != null) {
            mEdgeEngine!!.release()
            mEdgeEngine = null
        }
//        unRegistMarketListener()
        stopUpdateRankingTimer()
        unregistRankListener()
    }

    private fun getAppropriateLayoutResId(): Int {
        SP.set(IndexHelper.KEY_HOME_PAGE_LAYOUT_TYPE, "A")
        return R.layout.fragment_home
    }

    /***--------------Engine----------------------***/
    fun initEngine() {
        mEdgeEngine = EdgeEngine(requireActivity(), "home")
        mEdgeEngine?.registerAbility("homeBridge", HomeBridgeAbility::class.java)
//        mEdgeEngine?.registerWidget(AnimTextViewWidget.KEY, AnimTextViewWidget::class.java)
        mEdgeEngine?.registerWidget(HomeAssetTextView.KEY, HomeAssetTextView::class.java)
        mEdgeEngine?.registerWidget(HomeHBHomeCubeLivingView.KEY,HomeHBHomeCubeLivingView::class.java)
        mEdgeEngine?.registerWidget(HomeCubeCompositeView.KEY,HomeCubeCompositeView::class.java)
        mEdgeEngine?.runScript()
        mEdgeEngine?.sendEvent("initEngine()")
        sendCommonEvents()
        initRankListener()
    }

    fun sendCommonEvents() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendCommonConfig(mEdgeEngine)
        HomeEngineEvent.sendRateTypeStr(mEdgeEngine)

        HomeEngineEvent.isConditionMet.observe(this) { isTrue ->
            if (isTrue) {
                // 执行后续操作
                HomeEngineCore.refreshHomeView(mEdgeEngine,activity)
            }
        }
    }

    /***--------------UI----------------------***/
    override fun lodaHomeUI(homepageConfig: HomepageConfig?, userinfo: TransferAmountInfo?) {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            return
        }

        this.transferAmountInfo = userinfo
        if (this.homepageConfig == null) {
            Log.d("Home", "homepageConfig == null")
            this.homepageConfig = homepageConfig
            mFluentLayout?.removeAllViews()
            mNavigationContainer?.removeAllViews()
            HomeEngineCore.startRenderHome(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
        } else {
            this.homepageConfig = homepageConfig
            HomeEngineCore.refreshHomeView(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
            mRefreshLayout?.finishRefresh()
        }
    }

    override fun sendSymbolInfo() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
    }

    /**
     * 设置刷新
     */
    fun setRefreshLayout() {
        mRefreshLayout?.isEnableRefresh = true
        mRefreshLayout?.isEnableLoadMore = true
        mRefreshLayout?.setEnableLoadMoreWhenContentNotFull(false)
        mRefreshLayout?.setRefreshFooter(SmartRefreshFooter(activity))
        mCustomHeader = SmartRefreshHeader(activity)
        mRefreshLayout?.setRefreshHeader(mCustomHeader!!)
        mRefreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onLoadMore(refreshLayout: RefreshLayout) {
                DebugLog.i("home -----加载更多....")
                refreshFeedData(ACTION_UP)
//                SensorsDataHelper.track(
//                    "app_news_rechome_nrkpsl",
//                    HomeSensorsHelper.getFeedShowMap( HomeHelper.getCurrentTabType( mFeedViewPager, feedFragmentList))
//                )
            }

            override fun onRefresh(refreshLayout: RefreshLayout) {
                sendCommonEvents()
                //页面可见状态下拉刷新请求数据，否则停止刷新
                if (ui.isCanBeSeen) {
//                    HomeEngineCore.refreshHomeView(mEdgeEngine,activity)
//                    HomeEngineEvent.sendRefreshData(mEdgeEngine,)
                    presenter.getHomePageConfig()
                    refreshFeedData(ACTION_REFRESH)
//                    HomeEngineEvent.sendRefreshData(mEdgeEngine)
//                    SensorsDataHelper.track(
//                        "app_news_rechome_nrkpxl",
//                        HomeSensorsHelper.getFeedShowMap(HomeHelper.getCurrentTabType(mFeedViewPager, feedFragmentList)))
                } else {
                    ptrRefreshFinished(true)
                }
            }
        })
    }

    fun ptrRefreshFinished(refresh: Boolean) {
        val nowTime = DateTimeUtils.getDateTimeNow()
        val strTime = DateTimeUtils.formatStr(nowTime, DateTimeUtils.FORMAT_MM_DD_HH_MM_SS)
        mCustomHeader!!.setLastUpdateText(strTime)
        if (refresh) {
            mRefreshLayout?.finishRefresh()
            mRefreshLayout?.setNoMoreData(false)
        } else {
            mRefreshLayout?.finishLoadMore()
        }
    }

    fun isRefreshing(): Boolean {
        return mRefreshLayout?.isRefreshing ?: false
    }

    fun setRefreshState() {
        mRefreshLayout?.finishLoadMore()
    }

    private fun isLoading(): Boolean {
        return mRefreshLayout?.state == RefreshState.Loading
    }

    /***--------------事件监听----------------------***/
//    private fun registMarketListener() {
//        if (mProListener == null) {
//            mProListener = object : MarketOverviewListenerV2() {
//                override fun onSuccess(response: MarketOverviewV2Response) {
//                    if (response == null || response.data == null) return
//                    Log.d("Home", "------------MarketOverviewListenerV2---------------")
//                    val result = JSONObject()
//                    for ((key, symbolPriceV2) in response.data) {
//                        if (symbolPriceV2 == null) continue
//                        val item = JSONObject()
//                        item["decimalcPrice"] = symbolPriceV2.close
//                        item["decimalDelta"] = symbolPriceV2.rise * 100
//                        item["strAmount"] = symbolPriceV2.amount
//                        item["symbol"] = symbolPriceV2.symbol
//                        result[key] = item
//                    }
//                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
//                }
//            }
//        }
//        HbgProApi.getAPI().subscribeMarketOverview(true, mProListener)
//    }

//    private fun unRegistMarketListener() {
//        if (mProListener == null) return
//        HbgProApi.getAPI().subscribeMarketOverview(false, mProListener)
//    }

    private var mRankCache: ConcurrentHashMap<String, LastKlineResponse> = ConcurrentHashMap()
    private var mLastSymbols = mutableListOf<String>()
    private var mRankListener: LastKlineListener? = null
    private var mUpdateRankingInterval = 0L//0=不限制，1=限制频率1秒更新1次
    private fun initRankListener() {
        mEdgeEngine?.registerDataCallback("subRankingData", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    if (value == null) return
                    Log.d("homeFragment", "subRankingData value= ${value}")
                    val jsonObject = JSON.parseObject(value.toString())
                    if (jsonObject.containsKey("sub")) {
                        val subList: List<String> =
                            jsonObject.getJSONArray("sub").toJavaList(String::class.java)
                        if (subList.equals(mLastSymbols)) {
                            Log.d("homeFragment", "subRankingData subList is same, no need to update")
                            return
                        }
                        unregistRankListener()
                        mLastSymbols.clear()
                        mLastSymbols.addAll(subList)
                        registRankListener()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
        mEdgeEngine?.registerDataCallback("updateRankingInterval", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    Log.d("homeFragment", "updateRankingInterval value= ${value}")
                    if (value == null) return
                    val interval: Float? = value.toString().toFloatOrNull()
                    if (interval == null) return
                    val temp = (interval * 1000L).toLong()
                    if (mUpdateRankingInterval == temp) return
                    mUpdateRankingInterval = temp
                    if (mUpdateRankingInterval > 0) {
                        startUpdateRankingTimer()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
    }
    private var mTimer: Job? = null
    private fun startUpdateRankingTimer() {
        stopUpdateRankingTimer()
        mTimer = lifecycleScope.launch {
            while (true) {
                if (mRankCache.isNotEmpty()) {
                    val result = JSONObject()
                    for ((key, resp) in mRankCache) {
                        if (resp == null) continue
                        val item = JSONObject()
                        item["decimalcPrice"] = resp.tick.close
                        item["decimalDelta"] = resp.tick.changeRatio * 100
                        item["strAmount"] = resp.tick.amount
                        item["symbol"] = resp.symbol
                        result[resp.symbol] = item
                    }
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
                delay(mUpdateRankingInterval)
            }
        }
    }
    private fun stopUpdateRankingTimer() {
        if (mTimer != null && mTimer?.isActive == true) {
            mTimer?.cancel()
        }
    }

    private fun registRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        if (mUpdateRankingInterval > 0 && mTimer?.isActive != true) {
            startUpdateRankingTimer()
        }
        if (mRankListener == null) {
            mRankListener = object : LastKlineListener() {
                override fun onSuccess(response: LastKlineResponse) {
                    mRankCache.put(response.symbol, response)
                    //间隔大于0，走定时器更新
                    if (mUpdateRankingInterval > 0L) return
                    val result = JSONObject()
                    val item = JSONObject()
                    item["decimalcPrice"] = response.tick.close
                    item["decimalDelta"] = response.tick.changeRatio * 100
                    item["strAmount"] = response.tick.amount
                    item["symbol"] = response.symbol
                    result[response.symbol] = item
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
            }
        }
        HbgProApi.getAPI().subscribeBatch(
            true,
            mLastSymbols,
            mRankListener
        )
    }

    private fun unregistRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        HbgProApi.getAPI().subscribeBatch(false, mLastSymbols, mRankListener)
        mRankCache.clear()
    }

    private fun initRedPointListener() {
        RedPointHelper.getInstance().setChatListRedPointUiUpdateListener { baseRedPointNode ->
            val redCount = baseRedPointNode.redCount
            HomeEngineEvent.sendUnreadMessage(mEdgeEngine, redCount)
        }
    }

    private fun registLogin() {
        //登录成功回调
        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { integer: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                HomeEngineEvent.sendLoginStatus(mEdgeEngine)
                presenter.getTransferAmountInfo()
                HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            }
        }

        with(LiveKeyCons.HOME_TAB_CHANGE, Int::class.java).observe(this) { index: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                index?.let {
                    mFeedViewPager?.setCurrentItem(index, false)
                }
            }
        }

        with(LiveKeyCons.HOME_CONTENT_TAB_RED_POINT, HomeContentTabRedPointEvent::class.java).observeStick(
            this,
            { event: HomeContentTabRedPointEvent? ->
                Log.d("HomeFragment", "${LiveKeyCons.HOME_CONTENT_TAB_RED_POINT} event : ${event.toGsonStr}")
                if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                    // is nothing
                } else {
                    event?.let { it ->
                        changeRedPoint(it.index, it.isShow)
                    }
                }
            })
    }

    /**
     * 滑动状态处理
     */
    private val scrollStateHandler: Runnable = object : Runnable {
        override fun run() {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastScrollUpdate > 100) {
                lastScrollUpdate = -1
                feedScroll = false
            } else {
                HUIHandler.getInstance().postDelayed(this, 100)
            }
        }
    }

    fun isScroll(): Boolean {
        return feedScroll
    }

    private var viewHeight = 0
    private var verticalOffset = 0

    //  Feed流高度
    private var feedViewPageHeight = 0
    private fun homeIconEvent() {
        mFluentScrollView?.viewTreeObserver?.addOnGlobalLayoutListener {
            if (mFluentScrollView?.height ?: 0 > 0) {
                viewHeight = mFluentScrollView!!.height
            }
        }
        appBarLayout?.addOnOffsetChangedListener { _, verticalOffset ->
            this.verticalOffset = abs(verticalOffset)
            if (viewHeight - this.verticalOffset < 8) {
                playTabIconAnimation(true)
            } else {
                playTabIconAnimation(false)
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (viewHeight != 0) {
                (feedFragmentList?.getOrNull(0) as? FeedFragment)?.let { feedFragment ->
                    if (viewHeight + verticalOffset == 0) {
                        //   Feed流吸顶
                        feedFragment.homeStickyHeaderListener(true)
                    } else {
                        //   Feed流未吸顶
                        feedFragment.homeStickyHeaderListener(false)
                    }
                }
            }
        }
    }

    /**
     * playTabIconAnimation
     *
     * @param topIcon topIcon
     */
    private fun playTabIconAnimation(topIcon: Boolean) {
        if (isTopIcon != topIcon) {
            isTopIcon = topIcon
            animator = ValueAnimator.ofFloat(0f, 1f)
            animator?.addUpdateListener {
                val scale = it.animatedValue as Float
                val scaleDiff = 1f - scale
                homeCheckBoxBg?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBox?.alpha = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleX = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBoxIcon?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBoxIcon?.translationY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                } * (homeCheckBoxIcon?.height ?: 1)
            }
            animator?.duration = 300
            animator?.start()
        }
    }

    /**
     * when the user logout, change userInfo view
     * @param event Logout event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onEvent(event: LogOutEvent?) {
        mIsGetProToken = false
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todp is
            return
        }

        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        presenter.getTransferAmountInfo()
        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    /**
     * 登录成功  generateUserSig接口不会触发ticket换token，所以此处添加了token的监听来请求generateUserSig接口
     *
     * @param event ProTokenUpdate
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onProTokenUpdate(event: ProTokenUpdate) {
        val proToken = event.proToken
        Log.e("Home", "onProTokenUpdate proToken:$proToken getUI():$ui")
        if (!TextUtils.isEmpty(proToken) && ui != null) {
            presenter.getTransferAmountInfo()
        }
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendUnreadMessage(mEdgeEngine)
        mIsGetProToken = true;
    }

    override fun refreshHomeData() {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todo it
            return
        }

        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    override fun getWebviewContainer(): ViewGroup? {
        return mWebviewPoolContainer
    }

    override fun isGetProToken(): Boolean {
        return mIsGetProToken
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == 1001) {
            val resultString = data?.getStringExtra(CaptureActivity.RESULT_STRING)
            DebugLog.i("onActivityResult resultString=$resultString")
            if (ScanUtils.isScanLogin(resultString)) {
                UserCenterActionHelper.getInstance().checkLogin(activity, AuthTarget())
            } else if (ScanUtils.isScanFace(resultString)) {
                //安全扫码
                val uri = Uri.parse(resultString)
                val tsvToken = uri.getQueryParameter("tsvToken")
                DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "tsvToken=$tsvToken")
                if (checkSign(uri)) {
                    if (tsvToken != null) {
                        requestTsvMsg(tsvToken)
                    } else {
                        requestTsvMsg("")
                    }
                } else {
                    DebugLog.e(SecuritySetActivity.TAG_QR_SCAN, "checkSign Failed.")
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (ScanUtils.isScanOpen(resultString)) {
                try {
                    val redPacketResult = RedPacketManager.isRedPacket(resultString)
                    if (redPacketResult.isRedPacket) {
                        val codeWord = redPacketResult.codeWord
                        val aty = activity
                        if (null != aty) {
                            BaseModuleConfig.getCallback().checkLoginByRedPacket(aty, codeWord)
                        }
                    } else {
                        JumpUtils.getInstance().tryPushJumpUrl(Uri.parse(resultString)).checkIt().consumeJumpUrl()
                    }
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (!TextUtils.isEmpty(resultString) && resultString!!.startsWith("file:///android_asset/")) {
                HBBaseWebActivity.showWebView(activity, resultString, null, null, false)
            } else {
                //扫描内容无效提示
                HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
            }
        }
    }

    /**
     * 检查签名
     *
     * @param uri 扫码得到的uri
     * @return 签名通过
     */
    private fun checkSign(uri: Uri): Boolean {
        val ts = uri.getQueryParameter("ts")
        val sign = uri.getQueryParameter("sign")
        val localSign = MD5Utils.getMD5((ts + "Verify").toByteArray())
        return sign != null && sign.equals(localSign, ignoreCase = true)
    }

    /**
     * 请求提示语
     *
     * @param tsvToken 上个页面扫码获取的token
     */
    fun requestTsvMsg(tsvToken: String) {
        DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "requestTsvMsg")
        val param = HashMap<String, Any>()
        param["tsvToken"] = tsvToken
        param["uid"] = UserProvider.getInstance().currentId
        param["lang"] = AppLanguageHelper.getInstance().curLanguageHeader
        HRetrofit.risk<RiskService>(RiskService::class.java)
            .getTsvMsg(param)
            .compose<TsvMsg>(HRetrofit.riskIntCodeTransformer<TsvMsg>())
            .compose<TsvMsg>(RxJavaHelper.observeOnMainThread<TsvMsg>(ui))
            .subscribe(object : EasySubscriber<TsvMsg>() {
                override fun onStart() {
                    super.onStart()
                    <EMAIL>()
                }

                override fun onNext(tsvMsg: TsvMsg) {
                    super.onNext(tsvMsg)
                    HUIHandler.getInstance().postDelayed({ VerificationStartActivity.start(activity, tsvToken, tsvMsg.tsvMsg) }, 20)
                }

                override fun onAfter() {
                    super.onAfter()
                    <EMAIL>()
                }
            })
    }

    /**
     * 初始化 wifi 信息 用于安全信息采集等 Android 9.0 需求权限
     *
     * @param activity 上下文
     */
    fun initWifiInfo(activity: Activity?) {
        if (PhoneUtils.isCurVersionAboveAndroidP()) {
            val perms = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
            if (EasyPermissions.hasPermissions(context, *perms)) {
                PhoneUtils.getWifiSsid(activity)
                PhoneUtils.getMacAddress(activity)
                PhoneUtils.getNetworkType(activity)
            } else {
                EasyPermissions.requestPermissions(this, ConfigConstant.RC_FINE_LOCATION, *perms)
            }
        } else {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: List<String?>?) {
        if (ConfigConstant.RC_FINE_LOCATION == requestCode) {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: List<String?>) {
        DebugLog.i("onPermissionsGranted requestCode=$requestCode perms=$perms")
    }

    /**---------------------------feed--------------------------------**/
    private fun initViewPager() {
        coIndicator = viewFinder.find(R.id.coIndicator)
        mFeedTabLayoutGroup = viewFinder.find(R.id.home_feed_linear_tabLayout)
        mFeedViewPager = viewFinder.find(R.id.home_viewPager)
//        mFeedViewPager?.isSaveEnabled = false
        feedFragmentList = arrayListOf(
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_RECOMMEND),
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_FOLLOW),
            NewsChildFragment.getInstance(-100, "7*24"),
            LiveSquareHomeFragment.newInstance(-100, ""),
            AnnouncementFragment.newInstance()
        )
        tabs = arrayListOf(
            TabData(resources.getString(R.string.n_content_found), 0, 0),
            TabData(resources.getString(R.string.n_content_communityList_attention), 1, 1),
            TabData(resources.getString(R.string.n_content_newsflash), 2, 2),
            TabData(resources.getString(R.string.n_live), 3, 3),
            TabData(resources.getString(R.string.n_notice), 4, 4)
        )
        val adapter = BasePageAdapter(this)
        adapter.addDatas(feedFragmentList!!)
        mFeedViewPager?.adapter = adapter
        mFeedViewPager?.offscreenPageLimit = 1
        activity?.let {
            IndicatorHelper.initBottomNoLineIndicator(
                it,
                tabs!!,
                coIndicator!!,
                0f,
                mFeedViewPager!!,
                16f,
                R.attr.Text_L1,
                R.attr.Text_L3,
                scaleSize = 20f,
                isBold = true,
                onTabClick = object : TabClickListener {
                    override fun onTabClick(index: Int) {
                        android.util.Log.d("HomeFragment", "Tab点击事件: 索引=$index, 标题=${tabs?.getOrNull(index)?.name}")
                        if (nowCurrentContentPos != -1 && nowCurrentContentPos == index) {
                            android.util.Log.d("HomeFragment", "重复点击同一Tab，刷新数据")
                            refreshFeedData(ACTION_REFRESH)
                        }
                        nowCurrentContentPos = index
                        debugPluginStatus("Tab点击-索引$index")
                    }
                }
            )
            coIndicator?.listener = object : CoIndicator.PageSelectListener {
                override fun onSelected(position: Int) {
                    val item = tabs?.get(position)
                    if (0 == item?.type) {
                        SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
                    } else if (2 == item?.type) {
                        SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
                    } else if (3 == item?.type) {
                        SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
                    } else if (4 == item?.type) {
                        SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
                        changeRedPoint(4, false)
                    }
                }
            }
        }

        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { refreshFeedData(ACTION_REFRESH) }
//        try {
//            coIndicator?.run {
//                setSelectedTabIndicator(0)
//                for (i in 0..tabCount) {
//                    val tab = LayoutInflater.from(context).inflate(R.layout.home_new_content_tab, null)
//                    val title = tab.findViewById<TextView>(R.id.tvTabTitle)
//                    title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 14 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                    title.text = resources.getString(
//                        when (i) {
//                            0 -> {
//                                title.setTextColor(resources.getColor(R.color.baseColorPrimaryText))
//                                title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                                R.string.n_home_feed_tab_recommend
//                            }
//                            1 -> {
//                                R.string.n_content_communityList_attention
//                            }
//                            2 -> {
//                                R.string.n_home_feed_tab_news
//                            }
//                            3 -> {
//                                R.string.n_home_feed_tab_hot
//                            }
//                            4 -> {
//                                R.string.n_content_deep_news
//                            }
//                            else -> {
//                                R.string.n_live
//                            }
//                        }
//                    )
//                    getTabAt(i)?.customView = tab
//                }
//            }
//            coIndicator?.addOnTabSelectedListener(object : OnTabSelectedListener {
//                override fun onTabSelected(tab: TabLayout.Tab) {
//                    try {
//                        tab.customView?.let {
//                            val title = it.findViewById<TextView>(R.id.tvTabTitle)
//                            title.setTextColor(resources.getColor(R.color.baseColorPrimaryText))
//                            title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 20 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                            if (tab.position == 5) {
//                                SensorsDataHelper.track(
//                                    "app_recome_tab_click",
//                                    HomeSensorsHelper.getFeedShowMap(5)
//                                )
//                            }
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//
//                override fun onTabUnselected(tab: TabLayout.Tab) {
//                    try {
//                        tab.customView?.let {
//                            val title = it.findViewById<TextView>(R.id.tvTabTitle)
//                            title.setTextColor(resources.getColor(R.color.baseColorSecondaryTextNew))
//                            title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 14 * ViewUtils.getScreenWidth(context).toFloat() / 375.0f)
//                        }
//                    } catch (e: Exception) {
//                        e.printStackTrace()
//                    }
//                }
//
//                override fun onTabReselected(tab: TabLayout.Tab) {}
//            })
//        } catch (e: IndexOutOfBoundsException) {
//            e.printStackTrace()
//        }
//        HomeHelper.addListener(this, mFluentScrollerLayout, mRefreshLayout, mFeedViewPager, mTabLayout)
        coIndicator?.post { refreshFeedData(IndexInformationRequestData.ACTION_REFRESH) }
        // 增加FeedTablayout Group 事件
        addFeedTabListener()

        // 在ViewPager和Tab初始化完成后设置吸底Tab插件
        android.util.Log.d("HomeFragment", "ViewPager和Tab初始化完成，准备设置吸底Tab插件")
        setupStickyBottomTabPlugin()
    }

    private fun addFeedTabListener() {
//        mFluentScrollerLayout?.setOnStickyChangeListener { _: View?, newStickyView: View? ->
//            // Feed TabLayout 实现吸顶
//            if (newStickyView != null && newStickyView.id == mFeedTabLayoutGroup!!.id) {
//                mTabLayout?.setBackgroundResource(R.color.baseColorContentBackground)
//                mFeedTabLayoutGroup!!.setBackgroundResource(R.color.baseColorContentBackground)
//            } else {
//                mTabLayout?.setBackgroundResource(R.color.baseColorContentBackground)
//                mFeedTabLayoutGroup!!.setBackgroundResource(R.color.baseColorContentBackground)
//            }
//        }
    }

    override fun switchTabRefreshData(actionType: Int) {
        super.switchTabRefreshData(actionType)
//        val fragment = HomeHelper.getCurrentFragment(mFeedViewPager, feedFragmentList)
//        if (fragment != null) {
//            fragment.switchTabRefreshData(actionType)
//            return
//        }
//        SensorsDataHelper.track(
//            "app_recome_show",
//            HomeSensorsHelper.getFeedShowMap(
//                HomeHelper.getCurrentTabType(
//                    mFeedViewPager,
//                    feedFragmentList
//                )
//            )
//        )
        setRefreshState()
    }

    override fun refreshFeedPrevPage(position: Int) {
        super.refreshFeedPrevPage(position)
        setRefreshState()
    }

    override fun refreshFeedData(actionType: Int) {
        val fragment = feedFragmentList?.get(mFeedViewPager?.currentItem ?: 0)
        fragment?.let {
            when (it) {
                is FeedFragment -> {
                    it.refreshData(actionType)
                    return
                }

                is NewsChildFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.onRefresh(null)
                    }
                }

                is LiveSquareHomeFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.refreshPage()
                    }
                }

                else -> {}
            }
        }
        setRefreshState()

    }

    override fun onVisibleChangedFinal(visible: Boolean) {
        android.util.Log.d("HomeFragment", "=== Fragment可见性变化: $visible ===")
        super.onVisibleChangedFinal(visible)

        if(visible){
            android.util.Log.d("HomeFragment", "Fragment变为可见，启动相关服务...")
            HomeEngineEvent.sendLoginStatus(mEdgeEngine)
            presenter.getTransferAmountInfo()
            HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            ProOverviewController.getInstance().subOverview()

            // Fragment可见时重新配置导航栏适配并启用插件
            if (stickyBottomTabPlugin != null) {
                android.util.Log.d("HomeFragment", "Fragment可见，重新配置导航栏适配...")

                // 重新配置导航栏适配
                reconfigureNavigationBarAdaptation()

                android.util.Log.d("HomeFragment", "启用吸底Tab插件...")
                stickyBottomTabPlugin?.enable()
                android.util.Log.i("HomeFragment", " 吸底Tab插件已启用")

                // 重新添加调试滚动监听器
                addDebugScrollListener()

                debugPluginStatus("Fragment可见-启用插件")
            } else {
                android.util.Log.w("HomeFragment", " 吸底Tab插件为空，无法启用")
            }
        }else{
            android.util.Log.d("HomeFragment", "Fragment变为不可见，停止相关服务...")
            ProOverviewController.getInstance().unSubOverViewConditional()

            // Fragment不可见时禁用插件以节省资源
            if (stickyBottomTabPlugin != null) {
                android.util.Log.d("HomeFragment", "禁用吸底Tab插件以节省资源...")
                stickyBottomTabPlugin?.disable()
                android.util.Log.i("HomeFragment", " 吸底Tab插件已禁用")

                // 移除调试滚动监听器
                removeDebugScrollListener()

                debugPluginStatus("Fragment不可见-禁用插件")
            } else {
                android.util.Log.d("HomeFragment", "吸底Tab插件为空，无需禁用")
            }
        }
        checkFutureSub()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onVisibleChanged(visible: Boolean) {
        super.onVisibleChanged(visible)
        if (visible) {
            HomeEngineEvent.sendCommonConfig(mEdgeEngine)
            HomeEngineEvent.sendRateTypeStr(mEdgeEngine)
            HomeEngineEvent.sendPageAppear(mEdgeEngine)
//            Log.d("home","当前的单位："+HomeEngineEvent.checkPricingMethodChanged())
//            if(HomeEngineEvent.checkPricingMethodChanged()){

//            }
//            presenter.getHomePageConfig()
//            presenter.updateHomeUI()
            homeRadioContainer?.setOnTouchListener { _, _ ->
                if (!feedScroll && verticalOffset >= (mFluentScrollView?.height ?: 1)) {
                    setRefreshState()
                    val behavior = (appBarLayout?.layoutParams as LayoutParams?)?.behavior as AppBarLayout.Behavior?
                    behavior?.setTopAndBottomOffset(0)
                    appBarLayout?.setExpanded(true, true)
                    HUIHandler.getInstance().postDelayed({ refreshFeedData(ACTION_REFRESH) }, 300)
                }
                false
            }
            initRedPointListener()
//            registMarketListener()
            registRankListener()
        } else {
            HomeEngineEvent.sendPageDisappear(mEdgeEngine)
            homeRadioContainer?.setOnTouchListener(null)
//            unRegistMarketListener()
            stopUpdateRankingTimer()
            unregistRankListener()
        }
    }

    /**
     * Tab红点
     * @param index Int
     * @param isShow Boolean
     */
    private fun changeRedPoint(index: Int, isShow: Boolean = false) {
        try {
            (coIndicator?.navigator as? CommonNavigator)?.let { commonNavigator ->
                val titleView = commonNavigator.getPagerTitleView(index) as? RedPointPagerTitleView
                if (isShow) {
                    titleView?.showPoint()
                } else {
                    titleView?.hidePoint()
                }
            }
        } catch (e: Throwable) {
            e.printStackTrace()
            Log.d("HomeFragment", "changeRedPoint = $isShow , error = ${e.message ?: ""}")
        }
    }

    class AuthTarget : IAuthTarget, Serializable {
        override fun show(context: Context) {
            Log.d("Home", "---------show--------")
            LoginTokenHelper.getLoginTokenObservable().flatMap { s: String? ->
                HbgGlobalApi.getAPI().scanTokenBind(ScanUtils.getScanLoginCode(), 1, s).observable
            }.compose(RxJavaHelper.observeOnMainThread(null))
                .subscribe(object : EasySubscriber<TokenBindInfo>() {
                    override fun onStart() {
                        super.onStart()
//                        <EMAIL>()
                    }

                    override fun onNext(info: TokenBindInfo) {
                        super.onNext(info)
//                        <EMAIL>()
                        ScanLoginSuccessActivity.startScanLoginSuccessActivity(context, info)
                    }

                    override fun onError2(e: Throwable) {
                        super.onError2(e)
//                        <EMAIL>()
                    }

                    override fun onFailed(e: APIStatusErrorException) {
                        super.onFailed(e)
//                        <EMAIL>()
                    }
                })
        }

        constructor() {
        }

        companion object {
            private const val serialVersionUID = 5620774165925992463L
        }
    }
}