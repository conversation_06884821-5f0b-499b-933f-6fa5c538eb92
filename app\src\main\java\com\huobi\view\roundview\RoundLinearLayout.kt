package com.huobi.view.roundview

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.widget.LinearLayout
import com.ttv.demo.R

/**
 * 圆角LinearLayout
 */
class RoundLinearLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private var cornerRadius = 0f
    private var backgroundColor = 0
    private var isRippleEnable = false
    
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val path = Path()
    private val rectF = RectF()

    init {
        attrs?.let {
            val typedArray = context.obtainStyledAttributes(it, R.styleable.RoundLinearLayout)
            cornerRadius = typedArray.getDimension(R.styleable.RoundLinearLayout_rv_cornerRadius, 0f)
            backgroundColor = typedArray.getColor(R.styleable.RoundLinearLayout_rv_backgroundColor, 0)
            isRippleEnable = typedArray.getBoolean(R.styleable.RoundLinearLayout_rv_isRippleEnable, false)
            typedArray.recycle()
        }
        
        paint.color = backgroundColor
        paint.style = Paint.Style.FILL
        
        // 关闭硬件加速以支持clipPath
        setLayerType(LAYER_TYPE_SOFTWARE, null)
    }

    override fun onDraw(canvas: Canvas) {
        if (cornerRadius > 0) {
            rectF.set(0f, 0f, width.toFloat(), height.toFloat())
            path.reset()
            path.addRoundRect(rectF, cornerRadius, cornerRadius, Path.Direction.CW)
            canvas.clipPath(path)
            
            if (backgroundColor != 0) {
                canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)
            }
        }
        super.onDraw(canvas)
    }
}
