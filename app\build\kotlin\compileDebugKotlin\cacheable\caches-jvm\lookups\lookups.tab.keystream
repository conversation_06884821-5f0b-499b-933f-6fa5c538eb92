  
app_icon_size android.R.dimen  notification_large_icon_width android.R.dimen  content android.R.id  Animator android.animation  AnimatorListenerAdapter android.animation  
ValueAnimator android.animation  addListener android.animation.Animator  onAnimationEnd +android.animation.Animator.AnimatorListener  	Exception )android.animation.AnimatorListenerAdapter  android )android.animation.AnimatorListenerAdapter  config )android.animation.AnimatorListenerAdapter  hideStickyBottomTab )android.animation.AnimatorListenerAdapter  isAnimating )android.animation.AnimatorListenerAdapter  isBottomTabVisible )android.animation.AnimatorListenerAdapter  onAnimationCancel )android.animation.AnimatorListenerAdapter  onAnimationEnd )android.animation.AnimatorListenerAdapter  AnimatorUpdateListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  duration android.animation.ValueAnimator  interpolator android.animation.ValueAnimator  ofInt android.animation.ValueAnimator  start android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  Activity android.app  ContentFragment android.app.Activity  	Exception android.app.Activity  IntArray android.app.Activity  LayoutInflater android.app.Activity  LinearLayout android.app.Activity  R android.app.Activity  SmartRefreshLayout android.app.Activity  StickyBottomTabPluginFactory android.app.Activity  TabLayoutMediator android.app.Activity  TabPagerAdapter android.app.Activity  TextView android.app.Activity  android android.app.Activity  createForCoIndicator android.app.Activity  findViewById android.app.Activity  let android.app.Activity  listOf android.app.Activity  newInstance android.app.Activity  onCreate android.app.Activity  
trimIndent android.app.Activity  until android.app.Activity  
windowManager android.app.Activity  view android.app.Activity.android  widget android.app.Activity.android  View !android.app.Activity.android.view  	ImageView #android.app.Activity.android.widget  RelativeLayout #android.app.Activity.android.widget  TextView #android.app.Activity.android.widget  LayoutParams 2android.app.Activity.android.widget.RelativeLayout  Context android.content  ContentFragment android.content.Context  	Exception android.content.Context  IntArray android.content.Context  LayoutInflater android.content.Context  LinearLayout android.content.Context  R android.content.Context  SmartRefreshLayout android.content.Context  StickyBottomTabPluginFactory android.content.Context  TabLayoutMediator android.content.Context  TabPagerAdapter android.content.Context  TextView android.content.Context  android android.content.Context  createForCoIndicator android.content.Context  let android.content.Context  listOf android.content.Context  newInstance android.content.Context  obtainStyledAttributes android.content.Context  	resources android.content.Context  
trimIndent android.content.Context  until android.content.Context  view android.content.Context.android  widget android.content.Context.android  View $android.content.Context.android.view  	ImageView &android.content.Context.android.widget  RelativeLayout &android.content.Context.android.widget  TextView &android.content.Context.android.widget  LayoutParams 5android.content.Context.android.widget.RelativeLayout  ContentFragment android.content.ContextWrapper  	Exception android.content.ContextWrapper  IntArray android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  LinearLayout android.content.ContextWrapper  R android.content.ContextWrapper  SmartRefreshLayout android.content.ContextWrapper  StickyBottomTabPluginFactory android.content.ContextWrapper  TabLayoutMediator android.content.ContextWrapper  TabPagerAdapter android.content.ContextWrapper  TextView android.content.ContextWrapper  android android.content.ContextWrapper  createForCoIndicator android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  newInstance android.content.ContextWrapper  
trimIndent android.content.ContextWrapper  until android.content.ContextWrapper  view &android.content.ContextWrapper.android  widget &android.content.ContextWrapper.android  View +android.content.ContextWrapper.android.view  	ImageView -android.content.ContextWrapper.android.widget  RelativeLayout -android.content.ContextWrapper.android.widget  TextView -android.content.ContextWrapper.android.widget  LayoutParams <android.content.ContextWrapper.android.widget.RelativeLayout  
TypedArray android.content.res  displayMetrics android.content.res.Resources  getDimensionPixelSize android.content.res.Resources  
getIdentifier android.content.res.Resources  getResourceEntryName android.content.res.Resources  
getBoolean android.content.res.TypedArray  getColor android.content.res.TypedArray  getDimension android.content.res.TypedArray  recycle android.content.res.TypedArray  Canvas android.graphics  Insets android.graphics  Paint android.graphics  Path android.graphics  RectF android.graphics  clipPath android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  
parseColor android.graphics.Color  bottom android.graphics.Insets  ANTI_ALIAS_FLAG android.graphics.Paint  Style android.graphics.Paint  color android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  	Direction android.graphics.Path  addRoundRect android.graphics.Path  reset android.graphics.Path  CW android.graphics.Path.Direction  set android.graphics.RectF  Drawable android.graphics.drawable  Bundle 
android.os  getInt android.os.BaseBundle  	getString android.os.BaseBundle  putInt android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  getInt android.os.Bundle  	getString android.os.Bundle  let android.os.Bundle  putInt android.os.Bundle  	putString android.os.Bundle  AttributeSet android.util  DisplayMetrics android.util  let android.util.AttributeSet  density android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  WindowInsets android.view  
WindowMetrics android.view  ContentFragment  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  LayoutInflater  android.view.ContextThemeWrapper  LinearLayout  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  SmartRefreshLayout  android.view.ContextThemeWrapper  StickyBottomTabPluginFactory  android.view.ContextThemeWrapper  TabLayoutMediator  android.view.ContextThemeWrapper  TabPagerAdapter  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  createForCoIndicator  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  newInstance  android.view.ContextThemeWrapper  
trimIndent  android.view.ContextThemeWrapper  until  android.view.ContextThemeWrapper  view (android.view.ContextThemeWrapper.android  widget (android.view.ContextThemeWrapper.android  View -android.view.ContextThemeWrapper.android.view  	ImageView /android.view.ContextThemeWrapper.android.widget  RelativeLayout /android.view.ContextThemeWrapper.android.widget  TextView /android.view.ContextThemeWrapper.android.widget  LayoutParams >android.view.ContextThemeWrapper.android.widget.RelativeLayout  
getMetrics android.view.Display  getRealMetrics android.view.Display  BOTTOM android.view.Gravity  CENTER_VERTICAL android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  GONE android.view.View  
GRAVITY_START android.view.View  LAYER_TYPE_SOFTWARE android.view.View  MODE_SCROLLABLE android.view.View  OnClickListener android.view.View  OnLongClickListener android.view.View  OnTabSelectedListener android.view.View  Paint android.view.View  Path android.view.View  R android.view.View  RectF android.view.View  Tab android.view.View  VISIBLE android.view.View  animate android.view.View  context android.view.View  	elevation android.view.View  findViewById android.view.View  getLocationOnScreen android.view.View  height android.view.View  id android.view.View  	javaClass android.view.View  layoutParams android.view.View  let android.view.View  post android.view.View  postDelayed android.view.View  scrollY android.view.View  setBackgroundColor android.view.View  setLayerType android.view.View  setOnClickListener android.view.View  setOnLongClickListener android.view.View  setOnScrollChangeListener android.view.View  
setPadding android.view.View  translationY android.view.View  
visibility android.view.View  width android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> %android.view.View.OnLongClickListener  
GRAVITY_START android.view.ViewGroup  LAYER_TYPE_SOFTWARE android.view.ViewGroup  MODE_SCROLLABLE android.view.ViewGroup  OnTabSelectedListener android.view.ViewGroup  Paint android.view.ViewGroup  Path android.view.ViewGroup  R android.view.ViewGroup  RectF android.view.ViewGroup  Tab android.view.ViewGroup  addView android.view.ViewGroup  
childCount android.view.ViewGroup  
getChildAt android.view.ViewGroup  getLocationOnScreen android.view.ViewGroup  height android.view.ViewGroup  let android.view.ViewGroup  
removeView android.view.ViewGroup  width android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  bottomMargin )android.view.ViewGroup.MarginLayoutParams  
leftMargin )android.view.ViewGroup.MarginLayoutParams  rightMargin )android.view.ViewGroup.MarginLayoutParams  setDuration !android.view.ViewPropertyAnimator  setListener !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  translationY !android.view.ViewPropertyAnimator  getInsetsIgnoringVisibility android.view.WindowInsets  navigationBars android.view.WindowInsets.Type  currentWindowMetrics android.view.WindowManager  defaultDisplay android.view.WindowManager  windowInsets android.view.WindowMetrics  DecelerateInterpolator android.view.animation  	ImageView android.widget  LinearLayout android.widget  RelativeLayout android.widget  TextView android.widget  
GRAVITY_START android.widget.FrameLayout  MODE_SCROLLABLE android.widget.FrameLayout  OnTabSelectedListener android.widget.FrameLayout  Tab android.widget.FrameLayout  let android.widget.FrameLayout  
GRAVITY_START #android.widget.HorizontalScrollView  MODE_SCROLLABLE #android.widget.HorizontalScrollView  OnTabSelectedListener #android.widget.HorizontalScrollView  Tab #android.widget.HorizontalScrollView  let #android.widget.HorizontalScrollView  setColorFilter android.widget.ImageView  Gravity android.widget.LinearLayout  
HORIZONTAL android.widget.LinearLayout  LAYER_TYPE_SOFTWARE android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  Paint android.widget.LinearLayout  Path android.widget.LinearLayout  R android.widget.LinearLayout  RectF android.widget.LinearLayout  addView android.widget.LinearLayout  animate android.widget.LinearLayout  apply android.widget.LinearLayout  calculateOptimalElevation android.widget.LinearLayout  
childCount android.widget.LinearLayout  config android.widget.LinearLayout  	elevation android.widget.LinearLayout  
getChildAt android.widget.LinearLayout  gravity android.widget.LinearLayout  height android.widget.LinearLayout  layoutParams android.widget.LinearLayout  let android.widget.LinearLayout  onDraw android.widget.LinearLayout  orientation android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  setOnLongClickListener android.widget.LinearLayout  translationY android.widget.LinearLayout  
visibility android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  ABOVE android.widget.RelativeLayout  LayoutParams android.widget.RelativeLayout  addRule *android.widget.RelativeLayout.LayoutParams  
removeRule *android.widget.RelativeLayout.LayoutParams  setTextColor android.widget.TextView  text android.widget.TextView  ContentFragment #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  LayoutInflater #androidx.activity.ComponentActivity  LinearLayout #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  SmartRefreshLayout #androidx.activity.ComponentActivity  StickyBottomTabPluginFactory #androidx.activity.ComponentActivity  TabLayoutMediator #androidx.activity.ComponentActivity  TabPagerAdapter #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  createForCoIndicator #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  newInstance #androidx.activity.ComponentActivity  
trimIndent #androidx.activity.ComponentActivity  until #androidx.activity.ComponentActivity  view +androidx.activity.ComponentActivity.android  widget +androidx.activity.ComponentActivity.android  View 0androidx.activity.ComponentActivity.android.view  	ImageView 2androidx.activity.ComponentActivity.android.widget  RelativeLayout 2androidx.activity.ComponentActivity.android.widget  TextView 2androidx.activity.ComponentActivity.android.widget  LayoutParams Aandroidx.activity.ComponentActivity.android.widget.RelativeLayout  AppCompatActivity androidx.appcompat.app  ContentFragment (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  IntArray (androidx.appcompat.app.AppCompatActivity  LayoutInflater (androidx.appcompat.app.AppCompatActivity  LinearLayout (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  SmartRefreshLayout (androidx.appcompat.app.AppCompatActivity  StickyBottomTabPluginFactory (androidx.appcompat.app.AppCompatActivity  TabLayoutMediator (androidx.appcompat.app.AppCompatActivity  TabPagerAdapter (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  createForCoIndicator (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  newInstance (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  	resources (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  
trimIndent (androidx.appcompat.app.AppCompatActivity  until (androidx.appcompat.app.AppCompatActivity  view 0androidx.appcompat.app.AppCompatActivity.android  widget 0androidx.appcompat.app.AppCompatActivity.android  View 5androidx.appcompat.app.AppCompatActivity.android.view  	ImageView 7androidx.appcompat.app.AppCompatActivity.android.widget  RelativeLayout 7androidx.appcompat.app.AppCompatActivity.android.widget  TextView 7androidx.appcompat.app.AppCompatActivity.android.widget  LayoutParams Fandroidx.appcompat.app.AppCompatActivity.android.widget.RelativeLayout  CoordinatorLayout !androidx.coordinatorlayout.widget  LayoutParams 3androidx.coordinatorlayout.widget.CoordinatorLayout  addView 3androidx.coordinatorlayout.widget.CoordinatorLayout  
childCount 3androidx.coordinatorlayout.widget.CoordinatorLayout  
getChildAt 3androidx.coordinatorlayout.widget.CoordinatorLayout  post 3androidx.coordinatorlayout.widget.CoordinatorLayout  postDelayed 3androidx.coordinatorlayout.widget.CoordinatorLayout  
removeView 3androidx.coordinatorlayout.widget.CoordinatorLayout  AppBarLayout <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  R <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  android <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  until <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  LayoutParams Iandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.AppBarLayout  Gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  MATCH_PARENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  WRAP_CONTENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  actualBottomMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  apply @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  bottomMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  config @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  
leftMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  rightMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  Bundle #androidx.core.app.ComponentActivity  CoIndicator #androidx.core.app.ComponentActivity  ContentFragment #androidx.core.app.ComponentActivity  CoordinatorLayout #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  FragmentActivity #androidx.core.app.ComponentActivity  FragmentStateAdapter #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  LayoutInflater #androidx.core.app.ComponentActivity  LinearLayout #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  NestedScrollView #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  SmartRefreshLayout #androidx.core.app.ComponentActivity  StickyBottomTabPlugin #androidx.core.app.ComponentActivity  StickyBottomTabPluginFactory #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  TabLayoutMediator #androidx.core.app.ComponentActivity  TabPagerAdapter #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  
ViewPager2 #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  createForCoIndicator #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  newInstance #androidx.core.app.ComponentActivity  
trimIndent #androidx.core.app.ComponentActivity  until #androidx.core.app.ComponentActivity  view +androidx.core.app.ComponentActivity.android  widget +androidx.core.app.ComponentActivity.android  View 0androidx.core.app.ComponentActivity.android.view  	ImageView 2androidx.core.app.ComponentActivity.android.widget  RelativeLayout 2androidx.core.app.ComponentActivity.android.widget  TextView 2androidx.core.app.ComponentActivity.android.widget  LayoutParams Aandroidx.core.app.ComponentActivity.android.widget.RelativeLayout  NestedScrollView androidx.core.widget  OnScrollChangeListener %androidx.core.widget.NestedScrollView  scrollTo %androidx.core.widget.NestedScrollView  scrollY %androidx.core.widget.NestedScrollView  setOnScrollChangeListener %androidx.core.widget.NestedScrollView  let <androidx.core.widget.NestedScrollView.OnScrollChangeListener  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  ARG_POSITION androidx.fragment.app.Fragment  
ARG_TAB_TITLE androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  ContentFragment androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  onCreate androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  ContentFragment &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  IntArray &androidx.fragment.app.FragmentActivity  LayoutInflater &androidx.fragment.app.FragmentActivity  LinearLayout &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  SmartRefreshLayout &androidx.fragment.app.FragmentActivity  StickyBottomTabPluginFactory &androidx.fragment.app.FragmentActivity  TabLayoutMediator &androidx.fragment.app.FragmentActivity  TabPagerAdapter &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  createForCoIndicator &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  newInstance &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  
trimIndent &androidx.fragment.app.FragmentActivity  until &androidx.fragment.app.FragmentActivity  view .androidx.fragment.app.FragmentActivity.android  widget .androidx.fragment.app.FragmentActivity.android  View 3androidx.fragment.app.FragmentActivity.android.view  	ImageView 5androidx.fragment.app.FragmentActivity.android.widget  RelativeLayout 5androidx.fragment.app.FragmentActivity.android.widget  TextView 5androidx.fragment.app.FragmentActivity.android.widget  LayoutParams Dandroidx.fragment.app.FragmentActivity.android.widget.RelativeLayout  ContentFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  newInstance 1androidx.recyclerview.widget.RecyclerView.Adapter  FragmentStateAdapter androidx.viewpager2.adapter  ContentFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  newInstance 0androidx.viewpager2.adapter.FragmentStateAdapter  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  let %androidx.viewpager2.widget.ViewPager2  offscreenPageLimit %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  unregisterOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  	TabLayout :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  isBottomTabVisible :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  let :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  AppBarLayout "com.google.android.material.appbar  Behavior /com.google.android.material.appbar.AppBarLayout  LayoutParams /com.google.android.material.appbar.AppBarLayout  
childCount /com.google.android.material.appbar.AppBarLayout  
getChildAt /com.google.android.material.appbar.AppBarLayout  AppBarLayout <com.google.android.material.appbar.AppBarLayout.BaseBehavior  R <com.google.android.material.appbar.AppBarLayout.BaseBehavior  android <com.google.android.material.appbar.AppBarLayout.BaseBehavior  until <com.google.android.material.appbar.AppBarLayout.BaseBehavior  LayoutParams Icom.google.android.material.appbar.AppBarLayout.BaseBehavior.AppBarLayout  AppBarLayout 8com.google.android.material.appbar.AppBarLayout.Behavior  R 8com.google.android.material.appbar.AppBarLayout.Behavior  android 8com.google.android.material.appbar.AppBarLayout.Behavior  
onLayoutChild 8com.google.android.material.appbar.AppBarLayout.Behavior  onNestedScroll 8com.google.android.material.appbar.AppBarLayout.Behavior  until 8com.google.android.material.appbar.AppBarLayout.Behavior  LayoutParams Ecom.google.android.material.appbar.AppBarLayout.Behavior.AppBarLayout  SCROLL_FLAG_ENTER_ALWAYS <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SCROLL <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SNAP <com.google.android.material.appbar.AppBarLayout.LayoutParams  scrollFlags <com.google.android.material.appbar.AppBarLayout.LayoutParams  AppBarLayout 1com.google.android.material.appbar.HeaderBehavior  R 1com.google.android.material.appbar.HeaderBehavior  android 1com.google.android.material.appbar.HeaderBehavior  until 1com.google.android.material.appbar.HeaderBehavior  LayoutParams >com.google.android.material.appbar.HeaderBehavior.AppBarLayout  AppBarLayout 5com.google.android.material.appbar.ViewOffsetBehavior  R 5com.google.android.material.appbar.ViewOffsetBehavior  android 5com.google.android.material.appbar.ViewOffsetBehavior  until 5com.google.android.material.appbar.ViewOffsetBehavior  LayoutParams Bcom.google.android.material.appbar.ViewOffsetBehavior.AppBarLayout  	TabLayout  com.google.android.material.tabs  TabLayoutMediator  com.google.android.material.tabs  
GRAVITY_START *com.google.android.material.tabs.TabLayout  LinearLayout *com.google.android.material.tabs.TabLayout  MODE_SCROLLABLE *com.google.android.material.tabs.TabLayout  OnTabSelectedListener *com.google.android.material.tabs.TabLayout  Tab *com.google.android.material.tabs.TabLayout  addOnTabSelectedListener *com.google.android.material.tabs.TabLayout  addTab *com.google.android.material.tabs.TabLayout  apply *com.google.android.material.tabs.TabLayout  getTabAt *com.google.android.material.tabs.TabLayout  layoutParams *com.google.android.material.tabs.TabLayout  let *com.google.android.material.tabs.TabLayout  newTab *com.google.android.material.tabs.TabLayout  selectedTabPosition *com.google.android.material.tabs.TabLayout  setOnTabSelectedListener *com.google.android.material.tabs.TabLayout  tabCount *com.google.android.material.tabs.TabLayout  
tabGravity *com.google.android.material.tabs.TabLayout  tabMode *com.google.android.material.tabs.TabLayout  icon .com.google.android.material.tabs.TabLayout.Tab  let .com.google.android.material.tabs.TabLayout.Tab  position .com.google.android.material.tabs.TabLayout.Tab  select .com.google.android.material.tabs.TabLayout.Tab  text .com.google.android.material.tabs.TabLayout.Tab  TabConfigurationStrategy 2com.google.android.material.tabs.TabLayoutMediator  attach 2com.google.android.material.tabs.TabLayoutMediator  <SAM-CONSTRUCTOR> Kcom.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy  AttributeSet com.hbg.lib.widgets  Context com.hbg.lib.widgets  Int com.hbg.lib.widgets  JvmOverloads com.hbg.lib.widgets  LoadingRelativeLayout com.hbg.lib.widgets  RelativeLayout com.hbg.lib.widgets  AppBarLayout !com.hbg.module.libkt.custom.coord  AppBarLayoutBehavior !com.hbg.module.libkt.custom.coord  AttributeSet !com.hbg.module.libkt.custom.coord  Boolean !com.hbg.module.libkt.custom.coord  Context !com.hbg.module.libkt.custom.coord  CoordinatorLayout !com.hbg.module.libkt.custom.coord  Int !com.hbg.module.libkt.custom.coord  IntArray !com.hbg.module.libkt.custom.coord  R !com.hbg.module.libkt.custom.coord  View !com.hbg.module.libkt.custom.coord  android !com.hbg.module.libkt.custom.coord  until !com.hbg.module.libkt.custom.coord  Behavior .com.hbg.module.libkt.custom.coord.AppBarLayout  LayoutParams .com.hbg.module.libkt.custom.coord.AppBarLayout  AppBarLayout 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  R 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  android 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  handleStickyViewsOnScroll 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  isStickyView 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  processStickyViews 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  setupStickyBehavior 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  until 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  AttributeSet %com.hbg.module.libkt.custom.indicator  CoIndicator %com.hbg.module.libkt.custom.indicator  Context %com.hbg.module.libkt.custom.indicator  
GRAVITY_START %com.hbg.module.libkt.custom.indicator  Int %com.hbg.module.libkt.custom.indicator  JvmOverloads %com.hbg.module.libkt.custom.indicator  MODE_SCROLLABLE %com.hbg.module.libkt.custom.indicator  OnTabSelectedListener %com.hbg.module.libkt.custom.indicator  String %com.hbg.module.libkt.custom.indicator  Tab %com.hbg.module.libkt.custom.indicator  	TabLayout %com.hbg.module.libkt.custom.indicator  Unit %com.hbg.module.libkt.custom.indicator  let %com.hbg.module.libkt.custom.indicator  
GRAVITY_START 1com.hbg.module.libkt.custom.indicator.CoIndicator  LinearLayout 1com.hbg.module.libkt.custom.indicator.CoIndicator  MODE_SCROLLABLE 1com.hbg.module.libkt.custom.indicator.CoIndicator  addOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  addTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  android 1com.hbg.module.libkt.custom.indicator.CoIndicator  apply 1com.hbg.module.libkt.custom.indicator.CoIndicator  context 1com.hbg.module.libkt.custom.indicator.CoIndicator  getTabAt 1com.hbg.module.libkt.custom.indicator.CoIndicator  layoutParams 1com.hbg.module.libkt.custom.indicator.CoIndicator  let 1com.hbg.module.libkt.custom.indicator.CoIndicator  newTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  setOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  
setPadding 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabCount 1com.hbg.module.libkt.custom.indicator.CoIndicator  
tabGravity 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabMode 1com.hbg.module.libkt.custom.indicator.CoIndicator  Animator "com.hbg.module.libkt.custom.sticky  AnimatorListenerAdapter "com.hbg.module.libkt.custom.sticky  Boolean "com.hbg.module.libkt.custom.sticky  CoIndicator "com.hbg.module.libkt.custom.sticky  CoIndicatorAware "com.hbg.module.libkt.custom.sticky   CoIndicatorStickyBottomTabPlugin "com.hbg.module.libkt.custom.sticky  Config "com.hbg.module.libkt.custom.sticky  Context "com.hbg.module.libkt.custom.sticky  CoordinatorLayout "com.hbg.module.libkt.custom.sticky  	Exception "com.hbg.module.libkt.custom.sticky  Float "com.hbg.module.libkt.custom.sticky  Gravity "com.hbg.module.libkt.custom.sticky  Int "com.hbg.module.libkt.custom.sticky  IntArray "com.hbg.module.libkt.custom.sticky  LinearLayout "com.hbg.module.libkt.custom.sticky  List "com.hbg.module.libkt.custom.sticky  Long "com.hbg.module.libkt.custom.sticky  MutableList "com.hbg.module.libkt.custom.sticky  NestedScrollView "com.hbg.module.libkt.custom.sticky  StickyBottomTabPlugin "com.hbg.module.libkt.custom.sticky  StickyBottomTabPluginFactory "com.hbg.module.libkt.custom.sticky  String "com.hbg.module.libkt.custom.sticky  Suppress "com.hbg.module.libkt.custom.sticky  	TabLayout "com.hbg.module.libkt.custom.sticky  Unit "com.hbg.module.libkt.custom.sticky  	ViewGroup "com.hbg.module.libkt.custom.sticky  
ViewPager2 "com.hbg.module.libkt.custom.sticky  actualBottomMargin "com.hbg.module.libkt.custom.sticky  android "com.hbg.module.libkt.custom.sticky  androidx "com.hbg.module.libkt.custom.sticky  apply "com.hbg.module.libkt.custom.sticky  calculateOptimalElevation "com.hbg.module.libkt.custom.sticky  config "com.hbg.module.libkt.custom.sticky  contains "com.hbg.module.libkt.custom.sticky  	emptyList "com.hbg.module.libkt.custom.sticky  forEach "com.hbg.module.libkt.custom.sticky  handleTabClick "com.hbg.module.libkt.custom.sticky  hideStickyBottomTab "com.hbg.module.libkt.custom.sticky  isAnimating "com.hbg.module.libkt.custom.sticky  isBottomTabVisible "com.hbg.module.libkt.custom.sticky  
isNotEmpty "com.hbg.module.libkt.custom.sticky  	javaClass "com.hbg.module.libkt.custom.sticky  kotlin "com.hbg.module.libkt.custom.sticky  let "com.hbg.module.libkt.custom.sticky  	lowercase "com.hbg.module.libkt.custom.sticky  	maxOrNull "com.hbg.module.libkt.custom.sticky  
mutableListOf "com.hbg.module.libkt.custom.sticky  requireNotNull "com.hbg.module.libkt.custom.sticky  until "com.hbg.module.libkt.custom.sticky  CoIndicator Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin   CoIndicatorStickyBottomTabPlugin Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Config Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Context Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Float Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Int Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  LinearLayout Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  List Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  Long Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  NestedScrollView Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  String Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  
ViewPager2 Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  android Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  androidx Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  apply Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  config Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  context Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  	emptyList Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  handleCoIndicatorTabClick Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  isBottomTabVisible Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  let Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  performScrollToPosition Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  setTabTitles Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  	tabTitles Ccom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin  CoIndicator Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion   CoIndicatorStickyBottomTabPlugin Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  Config Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  LinearLayout Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  android Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  apply Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  	emptyList Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  isBottomTabVisible Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  let Mcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.Companion  OnPageChangeCallback Ncom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.ViewPager2  coordinatorlayout Lcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx  widget ^com.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx.coordinatorlayout  CoordinatorLayout ecom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin.androidx.coordinatorlayout.widget  OnScrollChangeListener 3com.hbg.module.libkt.custom.sticky.NestedScrollView  Animator 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  AnimatorListenerAdapter 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Boolean 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Builder 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  CoIndicator 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin   CoIndicatorStickyBottomTabPlugin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Config 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Context 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  CoordinatorLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	Exception 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Float 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Gravity 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Int 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  IntArray 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  LinearLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  List 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Long 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  MutableList 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  NestedScrollView 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  StickyBottomTabPlugin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  String 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Suppress 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	TabLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Unit 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	ViewGroup 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  
ViewPager2 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  actualBottomMargin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  android 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  androidx 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  apply 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  calculateOptimalElevation 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  checkBottomStickyTabVisibility 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  config 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  contains 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  context 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createGenericTabCopy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createStickyTabContainer 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createStickyTabLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  createTabLayoutCopy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  destroy 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  "detectExistingNavigationElevations 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  detectNavigationBars 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin   detectNavigationViewsRecursively 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  disable 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	emptyList 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  enable 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  getNavigationBarHeight 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  getStatusBarHeight 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  handleTabClick 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  hasNavigationBar 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  hideStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  isAnimating 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  isBottomTabVisible 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	isEnabled 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  
isInitialized 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  isNavigationRelatedView 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  
isNotEmpty 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	javaClass 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  kotlin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  let 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	lowercase 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  	maxOrNull 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  
mutableListOf 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  navigationBarHeight 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  nestedScrollChangeListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  pageChangeCallback 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  performScrollToPosition 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  removeScrollListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  requireNotNull 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupScrollListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupTabSync 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  showInitialStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  showStickyBottomTab 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  #showStickyBottomTabWithoutAnimation 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  statusBarHeight 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  stickyBottomTabContainer 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  until 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  Config @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  StickyBottomTabPlugin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  animationDuration @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  apply @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  autoDetectNavigationBar @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  bottomMargin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  build @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  context @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  coordinatorLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  horizontalMargin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  maxElevation @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  nestedScrollView @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  onTabClickListener @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  originalTabLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  requireNotNull @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  scrollThreshold @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  scrollToPosition @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setAnimationDuration @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setAutoDetectNavigationBar @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setBottomMargin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setCoordinatorLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setHorizontalMargin @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setMaxElevation @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setNestedScrollView @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setOnTabClickListener @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setOriginalTabLayout @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setScrollThreshold @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setScrollToPosition @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  setViewPager @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  tabBackgroundColor @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  tabElevation @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  	viewPager @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Builder  animationDuration ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  autoDetectNavigationBar ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  bottomMargin ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  coordinatorLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  horizontalMargin ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  maxElevation ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  nestedScrollView ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  onTabClickListener ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  originalTabLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  scrollThreshold ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  scrollToPosition ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  tabBackgroundColor ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  tabElevation ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  	viewPager ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.Config  OnScrollChangeListener Icom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.NestedScrollView  OnTabSelectedListener Bcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.TabLayout  Tab Bcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.TabLayout  OnPageChangeCallback Ccom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.ViewPager2  	animation @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android  app @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android  view @com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android  Animator Jcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android.animation  AnimatorListenerAdapter Jcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android.animation  Activity Dcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android.app  View Ecom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android.view  	ViewGroup Ecom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.android.view  coordinatorlayout Acom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx  widget Scom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.coordinatorlayout  CoordinatorLayout Zcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.androidx.coordinatorlayout.widget  Boolean ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoIndicator ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoIndicatorAware ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Context ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  CoordinatorLayout ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Float ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Int ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  List ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  Long ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  NestedScrollView ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  StickyBottomTabPlugin ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  String ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  
ViewPager2 ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  apply ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  createForCoIndicator ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  handleCoIndicatorClick ?com.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory  setTabTitles Pcom.hbg.module.libkt.custom.sticky.StickyBottomTabPluginFactory.CoIndicatorAware  OnTabSelectedListener ,com.hbg.module.libkt.custom.sticky.TabLayout  Tab ,com.hbg.module.libkt.custom.sticky.TabLayout  OnPageChangeCallback -com.hbg.module.libkt.custom.sticky.ViewPager2  	animation *com.hbg.module.libkt.custom.sticky.android  app *com.hbg.module.libkt.custom.sticky.android  view *com.hbg.module.libkt.custom.sticky.android  Animator 4com.hbg.module.libkt.custom.sticky.android.animation  AnimatorListenerAdapter 4com.hbg.module.libkt.custom.sticky.android.animation  Activity .com.hbg.module.libkt.custom.sticky.android.app  View /com.hbg.module.libkt.custom.sticky.android.view  	ViewGroup /com.hbg.module.libkt.custom.sticky.android.view  coordinatorlayout +com.hbg.module.libkt.custom.sticky.androidx  widget =com.hbg.module.libkt.custom.sticky.androidx.coordinatorlayout  CoordinatorLayout Dcom.hbg.module.libkt.custom.sticky.androidx.coordinatorlayout.widget  AttributeSet com.huobi.view  Context com.huobi.view  Int com.huobi.view  JvmOverloads com.huobi.view  MyNestedScrollView com.huobi.view  NestedScrollView com.huobi.view  AttributeSet com.huobi.view.roundview  Canvas com.huobi.view.roundview  Context com.huobi.view.roundview  Int com.huobi.view.roundview  JvmOverloads com.huobi.view.roundview  LAYER_TYPE_SOFTWARE com.huobi.view.roundview  LinearLayout com.huobi.view.roundview  Paint com.huobi.view.roundview  Path com.huobi.view.roundview  R com.huobi.view.roundview  RectF com.huobi.view.roundview  RoundLinearLayout com.huobi.view.roundview  let com.huobi.view.roundview  LAYER_TYPE_SOFTWARE *com.huobi.view.roundview.RoundLinearLayout  Paint *com.huobi.view.roundview.RoundLinearLayout  Path *com.huobi.view.roundview.RoundLinearLayout  R *com.huobi.view.roundview.RoundLinearLayout  RectF *com.huobi.view.roundview.RoundLinearLayout  backgroundColor *com.huobi.view.roundview.RoundLinearLayout  cornerRadius *com.huobi.view.roundview.RoundLinearLayout  height *com.huobi.view.roundview.RoundLinearLayout  isRippleEnable *com.huobi.view.roundview.RoundLinearLayout  let *com.huobi.view.roundview.RoundLinearLayout  paint *com.huobi.view.roundview.RoundLinearLayout  path *com.huobi.view.roundview.RoundLinearLayout  rectF *com.huobi.view.roundview.RoundLinearLayout  setLayerType *com.huobi.view.roundview.RoundLinearLayout  width *com.huobi.view.roundview.RoundLinearLayout  SmartRefreshLayout com.scwang.smart.refresh.layout  layoutParams 2com.scwang.smart.refresh.layout.SmartRefreshLayout  let 2com.scwang.smart.refresh.layout.SmartRefreshLayout  ARG_POSITION com.ttv.demo  
ARG_TAB_TITLE com.ttv.demo  AppCompatActivity com.ttv.demo  Bundle com.ttv.demo  CoIndicator com.ttv.demo  ContentFragment com.ttv.demo  CoordinatorLayout com.ttv.demo  	Exception com.ttv.demo  Fragment com.ttv.demo  FragmentActivity com.ttv.demo  FragmentStateAdapter com.ttv.demo  Int com.ttv.demo  IntArray com.ttv.demo  LayoutInflater com.ttv.demo  LinearLayout com.ttv.demo  List com.ttv.demo  NestedScrollView com.ttv.demo  R com.ttv.demo  SmartRefreshLayout com.ttv.demo  StickyBottomTabDemoActivity com.ttv.demo  StickyBottomTabPlugin com.ttv.demo  StickyBottomTabPluginFactory com.ttv.demo  String com.ttv.demo  TabLayoutMediator com.ttv.demo  TabPagerAdapter com.ttv.demo  TextView com.ttv.demo  View com.ttv.demo  	ViewGroup com.ttv.demo  
ViewPager2 com.ttv.demo  android com.ttv.demo  createForCoIndicator com.ttv.demo  forEach com.ttv.demo  let com.ttv.demo  listOf com.ttv.demo  newInstance com.ttv.demo  
trimIndent com.ttv.demo  until com.ttv.demo  ARG_POSITION com.ttv.demo.ContentFragment  
ARG_TAB_TITLE com.ttv.demo.ContentFragment  Bundle com.ttv.demo.ContentFragment  	Companion com.ttv.demo.ContentFragment  ContentFragment com.ttv.demo.ContentFragment  Int com.ttv.demo.ContentFragment  LayoutInflater com.ttv.demo.ContentFragment  R com.ttv.demo.ContentFragment  String com.ttv.demo.ContentFragment  TextView com.ttv.demo.ContentFragment  View com.ttv.demo.ContentFragment  	ViewGroup com.ttv.demo.ContentFragment  	arguments com.ttv.demo.ContentFragment  let com.ttv.demo.ContentFragment  newInstance com.ttv.demo.ContentFragment  position com.ttv.demo.ContentFragment  setupContentForTab com.ttv.demo.ContentFragment  tabTitle com.ttv.demo.ContentFragment  ARG_POSITION &com.ttv.demo.ContentFragment.Companion  
ARG_TAB_TITLE &com.ttv.demo.ContentFragment.Companion  Bundle &com.ttv.demo.ContentFragment.Companion  ContentFragment &com.ttv.demo.ContentFragment.Companion  R &com.ttv.demo.ContentFragment.Companion  let &com.ttv.demo.ContentFragment.Companion  newInstance &com.ttv.demo.ContentFragment.Companion  clLayout com.ttv.demo.R.id  coIndicator com.ttv.demo.R.id  fluent_container com.ttv.demo.R.id  fluent_content_nsv com.ttv.demo.R.id  fluent_refresh_layout com.ttv.demo.R.id  home_feed_linear_tabLayout com.ttv.demo.R.id  home_viewPager com.ttv.demo.R.id  main_tab com.ttv.demo.R.id  tv_card_content com.ttv.demo.R.id  
tv_card_title com.ttv.demo.R.id  tv_tab_title com.ttv.demo.R.id  
activity_main com.ttv.demo.R.layout  content_card com.ttv.demo.R.layout  fragment_content com.ttv.demo.R.layout  RoundLinearLayout com.ttv.demo.R.styleable  $RoundLinearLayout_rv_backgroundColor com.ttv.demo.R.styleable  !RoundLinearLayout_rv_cornerRadius com.ttv.demo.R.styleable  #RoundLinearLayout_rv_isRippleEnable com.ttv.demo.R.styleable  Bundle (com.ttv.demo.StickyBottomTabDemoActivity  CoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  ContentFragment (com.ttv.demo.StickyBottomTabDemoActivity  CoordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  	Exception (com.ttv.demo.StickyBottomTabDemoActivity  Fragment (com.ttv.demo.StickyBottomTabDemoActivity  FragmentActivity (com.ttv.demo.StickyBottomTabDemoActivity  FragmentStateAdapter (com.ttv.demo.StickyBottomTabDemoActivity  Int (com.ttv.demo.StickyBottomTabDemoActivity  IntArray (com.ttv.demo.StickyBottomTabDemoActivity  LayoutInflater (com.ttv.demo.StickyBottomTabDemoActivity  LinearLayout (com.ttv.demo.StickyBottomTabDemoActivity  List (com.ttv.demo.StickyBottomTabDemoActivity  NestedScrollView (com.ttv.demo.StickyBottomTabDemoActivity  R (com.ttv.demo.StickyBottomTabDemoActivity  SmartRefreshLayout (com.ttv.demo.StickyBottomTabDemoActivity  StickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  StickyBottomTabPluginFactory (com.ttv.demo.StickyBottomTabDemoActivity  String (com.ttv.demo.StickyBottomTabDemoActivity  TabLayoutMediator (com.ttv.demo.StickyBottomTabDemoActivity  TabPagerAdapter (com.ttv.demo.StickyBottomTabDemoActivity  TextView (com.ttv.demo.StickyBottomTabDemoActivity  
ViewPager2 (com.ttv.demo.StickyBottomTabDemoActivity  android (com.ttv.demo.StickyBottomTabDemoActivity  coIndicator (com.ttv.demo.StickyBottomTabDemoActivity  coordinatorLayout (com.ttv.demo.StickyBottomTabDemoActivity  createForCoIndicator (com.ttv.demo.StickyBottomTabDemoActivity  detectBottomNavigationHeight (com.ttv.demo.StickyBottomTabDemoActivity  findViewById (com.ttv.demo.StickyBottomTabDemoActivity  fluentContainer (com.ttv.demo.StickyBottomTabDemoActivity  	initViews (com.ttv.demo.StickyBottomTabDemoActivity  let (com.ttv.demo.StickyBottomTabDemoActivity  listOf (com.ttv.demo.StickyBottomTabDemoActivity  nestedScrollView (com.ttv.demo.StickyBottomTabDemoActivity  newInstance (com.ttv.demo.StickyBottomTabDemoActivity  $reconfigurePluginForNavigationChange (com.ttv.demo.StickyBottomTabDemoActivity  	resources (com.ttv.demo.StickyBottomTabDemoActivity  setContentView (com.ttv.demo.StickyBottomTabDemoActivity  setupContent (com.ttv.demo.StickyBottomTabDemoActivity  setupNavigationBarInteraction (com.ttv.demo.StickyBottomTabDemoActivity  setupNavigationTabClicks (com.ttv.demo.StickyBottomTabDemoActivity  setupStickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  setupTabLayout (com.ttv.demo.StickyBottomTabDemoActivity  setupViewPager (com.ttv.demo.StickyBottomTabDemoActivity  stickyBottomTabPlugin (com.ttv.demo.StickyBottomTabDemoActivity  tabLayoutContainer (com.ttv.demo.StickyBottomTabDemoActivity  	tabTitles (com.ttv.demo.StickyBottomTabDemoActivity  toggleNavigationBarVisibility (com.ttv.demo.StickyBottomTabDemoActivity  
trimIndent (com.ttv.demo.StickyBottomTabDemoActivity  until (com.ttv.demo.StickyBottomTabDemoActivity  updateNavigationTabSelection (com.ttv.demo.StickyBottomTabDemoActivity  verifyNavigationBarAdaptation (com.ttv.demo.StickyBottomTabDemoActivity  	viewPager (com.ttv.demo.StickyBottomTabDemoActivity  ContentFragment 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  newInstance 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  	tabTitles 8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter  view 0com.ttv.demo.StickyBottomTabDemoActivity.android  widget 0com.ttv.demo.StickyBottomTabDemoActivity.android  View 5com.ttv.demo.StickyBottomTabDemoActivity.android.view  	ImageView 7com.ttv.demo.StickyBottomTabDemoActivity.android.widget  RelativeLayout 7com.ttv.demo.StickyBottomTabDemoActivity.android.widget  TextView 7com.ttv.demo.StickyBottomTabDemoActivity.android.widget  LayoutParams Fcom.ttv.demo.StickyBottomTabDemoActivity.android.widget.RelativeLayout  view com.ttv.demo.android  widget com.ttv.demo.android  View com.ttv.demo.android.view  	ImageView com.ttv.demo.android.widget  RelativeLayout com.ttv.demo.android.widget  TextView com.ttv.demo.android.widget  LayoutParams *com.ttv.demo.android.widget.RelativeLayout  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  
simpleName java.lang.Class  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function5 kotlin  IntArray kotlin  Nothing kotlin  Suppress kotlin  apply kotlin  let kotlin  requireNotNull kotlin  not kotlin.Boolean  	compareTo kotlin.Float  plus kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  get kotlin.IntArray  toInt kotlin.Long  contains 
kotlin.String  plus 
kotlin.String  
trimIndent 
kotlin.String  IntIterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  	maxOrNull kotlin.collections  
mutableListOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.List  
isNotEmpty kotlin.collections.List  	maxOrNull kotlin.collections.List  size kotlin.collections.List  add kotlin.collections.MutableList  JvmOverloads 
kotlin.jvm  	javaClass 
kotlin.jvm  kotlin 
kotlin.jvm  max kotlin.math  min kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  contains kotlin.sequences  forEach kotlin.sequences  	maxOrNull kotlin.sequences  contains kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  	lowercase kotlin.text  	maxOrNull kotlin.text  
trimIndent kotlin.text  LayoutParams 4com.hbg.module.libkt.custom.sticky.CoordinatorLayout  checkInitialStickyTabState 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  updateBottomMargin 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  LayoutParams Jcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.CoordinatorLayout  getLocationInWindow android.view.View  getLocationInWindow android.view.ViewGroup  getLocationInWindow %androidx.core.widget.NestedScrollView  height %androidx.core.widget.NestedScrollView  android android.view.View  android android.view.ViewGroup  android android.widget.FrameLayout  android %androidx.core.widget.NestedScrollView  onNestedScroll %androidx.core.widget.NestedScrollView  onScrollChanged %androidx.core.widget.NestedScrollView  java "com.hbg.module.libkt.custom.sticky  java 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  android com.huobi.view  android !com.huobi.view.MyNestedScrollView  scrollY !com.huobi.view.MyNestedScrollView  view com.huobi.view.android  View com.huobi.view.android.view  name java.lang.Class  java 
kotlin.jvm  java kotlin.reflect.KClass  OnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  addOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  removeOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  totalScrollRange /com.google.android.material.appbar.AppBarLayout  let Gcom.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener  com "com.hbg.module.libkt.custom.sticky  format "com.hbg.module.libkt.custom.sticky  appBarOffsetChangeListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  &checkBottomStickyTabVisibilityByOffset 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  com 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  findAppBarLayout 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  format 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupAppBarOffsetListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  setupNestedScrollListener 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin  google <com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com  android Ccom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google  material Kcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android  appbar Tcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android.material  AppBarLayout [com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android.material.appbar  OnOffsetChangedListener hcom.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin.com.google.android.material.appbar.AppBarLayout  google &com.hbg.module.libkt.custom.sticky.com  android -com.hbg.module.libkt.custom.sticky.com.google  material 5com.hbg.module.libkt.custom.sticky.com.google.android  appbar >com.hbg.module.libkt.custom.sticky.com.google.android.material  AppBarLayout Ecom.hbg.module.libkt.custom.sticky.com.google.android.material.appbar  OnOffsetChangedListener Rcom.hbg.module.libkt.custom.sticky.com.google.android.material.appbar.AppBarLayout  String kotlin  div kotlin.Float  	Companion 
kotlin.String  format 
kotlin.String  format kotlin.String.Companion  abs kotlin.math  format kotlin.text  ARG_POSITION android.app.Activity  	ARG_TITLE android.app.Activity  Bundle android.app.Activity  DemoFragment android.app.Activity  Log android.app.Activity  TabFragmentAdapter android.app.Activity  View android.app.Activity  ARG_POSITION android.content.Context  	ARG_TITLE android.content.Context  Bundle android.content.Context  DemoFragment android.content.Context  Log android.content.Context  TabFragmentAdapter android.content.Context  View android.content.Context  ARG_POSITION android.content.ContextWrapper  	ARG_TITLE android.content.ContextWrapper  Bundle android.content.ContextWrapper  DemoFragment android.content.ContextWrapper  Log android.content.ContextWrapper  TabFragmentAdapter android.content.ContextWrapper  View android.content.ContextWrapper  Log android.util  ARG_POSITION  android.view.ContextThemeWrapper  	ARG_TITLE  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  DemoFragment  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  TabFragmentAdapter  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  paddingLeft android.view.View  paddingRight android.view.View  
paddingTop android.view.View  
setPadding android.widget.TextView  textSize android.widget.TextView  ARG_POSITION #androidx.activity.ComponentActivity  	ARG_TITLE #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  DemoFragment #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  TabFragmentAdapter #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  ARG_POSITION (androidx.appcompat.app.AppCompatActivity  	ARG_TITLE (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  DemoFragment (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  TabFragmentAdapter (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  ARG_POSITION #androidx.core.app.ComponentActivity  	ARG_TITLE #androidx.core.app.ComponentActivity  DemoFragment #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  TabFragmentAdapter #androidx.core.app.ComponentActivity  	TabLayout #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  	ViewGroup #androidx.core.app.ComponentActivity  	ARG_TITLE androidx.fragment.app.Fragment  DemoFragment androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  ARG_POSITION &androidx.fragment.app.FragmentActivity  	ARG_TITLE &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  DemoFragment &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  TabFragmentAdapter &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  DemoFragment 1androidx.recyclerview.widget.RecyclerView.Adapter  DemoFragment 0androidx.viewpager2.adapter.FragmentStateAdapter  	ARG_TITLE com.ttv.demo  DemoFragment com.ttv.demo  Log com.ttv.demo  TabFragmentAdapter com.ttv.demo  	TabLayout com.ttv.demo  bottom_navigation_container com.ttv.demo.R.id  bottom_tab_layout com.ttv.demo.R.id  ARG_POSITION (com.ttv.demo.StickyBottomTabDemoActivity  	ARG_TITLE (com.ttv.demo.StickyBottomTabDemoActivity  DemoFragment (com.ttv.demo.StickyBottomTabDemoActivity  Log (com.ttv.demo.StickyBottomTabDemoActivity  TabFragmentAdapter (com.ttv.demo.StickyBottomTabDemoActivity  	TabLayout (com.ttv.demo.StickyBottomTabDemoActivity  View (com.ttv.demo.StickyBottomTabDemoActivity  	ViewGroup (com.ttv.demo.StickyBottomTabDemoActivity  bottomTabLayout (com.ttv.demo.StickyBottomTabDemoActivity  getNavigationBarHeight (com.ttv.demo.StickyBottomTabDemoActivity  setupBottomNavigationPadding (com.ttv.demo.StickyBottomTabDemoActivity  setupBottomTabSync (com.ttv.demo.StickyBottomTabDemoActivity  ARG_POSITION 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	ARG_TITLE 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  Bundle 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	Companion 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  DemoFragment 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  Int 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  LayoutInflater 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  String 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  TextView 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  View 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	ViewGroup 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  	arguments 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  newInstance 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  requireContext 5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment  ARG_POSITION ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  	ARG_TITLE ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  Bundle ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  DemoFragment ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  TextView ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  newInstance ?com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment.Companion  DemoFragment ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter  newInstance ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter  	tabTitles ;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           