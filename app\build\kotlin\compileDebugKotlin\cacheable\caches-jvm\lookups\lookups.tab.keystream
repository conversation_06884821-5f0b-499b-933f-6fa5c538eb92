  content android.R.id  Animator android.animation  AnimatorListenerAdapter android.animation  
ValueAnimator android.animation  addListener android.animation.Animator  onAnimationEnd +android.animation.Animator.AnimatorListener  	Exception )android.animation.AnimatorListenerAdapter  android )android.animation.AnimatorListenerAdapter  config )android.animation.AnimatorListenerAdapter  hideStickyBottomTab )android.animation.AnimatorListenerAdapter  isAnimating )android.animation.AnimatorListenerAdapter  isBottomTabVisible )android.animation.AnimatorListenerAdapter  onAnimationCancel )android.animation.AnimatorListenerAdapter  onAnimationEnd )android.animation.AnimatorListenerAdapter  AnimatorUpdateListener android.animation.ValueAnimator  addUpdateListener android.animation.ValueAnimator  
animatedValue android.animation.ValueAnimator  duration android.animation.ValueAnimator  interpolator android.animation.ValueAnimator  ofInt android.animation.ValueAnimator  start android.animation.ValueAnimator  <SAM-CONSTRUCTOR> 6android.animation.ValueAnimator.AnimatorUpdateListener  Activity android.app  findViewById android.app.Activity  
windowManager android.app.Activity  Context android.content  obtainStyledAttributes android.content.Context  	resources android.content.Context  
TypedArray android.content.res  displayMetrics android.content.res.Resources  getDimensionPixelSize android.content.res.Resources  
getIdentifier android.content.res.Resources  getResourceEntryName android.content.res.Resources  
getBoolean android.content.res.TypedArray  getColor android.content.res.TypedArray  getDimension android.content.res.TypedArray  recycle android.content.res.TypedArray  Canvas android.graphics  Paint android.graphics  Path android.graphics  RectF android.graphics  clipPath android.graphics.Canvas  
drawRoundRect android.graphics.Canvas  ANTI_ALIAS_FLAG android.graphics.Paint  Style android.graphics.Paint  color android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  	Direction android.graphics.Path  addRoundRect android.graphics.Path  reset android.graphics.Path  CW android.graphics.Path.Direction  set android.graphics.RectF  Drawable android.graphics.drawable  Bundle 
android.os  getInt android.os.BaseBundle  	getString android.os.BaseBundle  putInt android.os.BaseBundle  	putString android.os.BaseBundle  getInt android.os.Bundle  	getString android.os.Bundle  let android.os.Bundle  putInt android.os.Bundle  	putString android.os.Bundle  AttributeSet android.util  DisplayMetrics android.util  let android.util.AttributeSet  density android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  
getMetrics android.view.Display  getRealMetrics android.view.Display  BOTTOM android.view.Gravity  CENTER_VERTICAL android.view.Gravity  inflate android.view.LayoutInflater  
GRAVITY_START android.view.View  LAYER_TYPE_SOFTWARE android.view.View  MODE_SCROLLABLE android.view.View  OnTabSelectedListener android.view.View  Paint android.view.View  Path android.view.View  R android.view.View  RectF android.view.View  Tab android.view.View  android android.view.View  animate android.view.View  	elevation android.view.View  findViewById android.view.View  getLocationOnScreen android.view.View  height android.view.View  id android.view.View  	javaClass android.view.View  layoutParams android.view.View  let android.view.View  post android.view.View  postDelayed android.view.View  scrollY android.view.View  setBackgroundColor android.view.View  setLayerType android.view.View  translationY android.view.View  width android.view.View  
GRAVITY_START android.view.ViewGroup  LAYER_TYPE_SOFTWARE android.view.ViewGroup  MODE_SCROLLABLE android.view.ViewGroup  OnTabSelectedListener android.view.ViewGroup  Paint android.view.ViewGroup  Path android.view.ViewGroup  R android.view.ViewGroup  RectF android.view.ViewGroup  Tab android.view.ViewGroup  addView android.view.ViewGroup  android android.view.ViewGroup  
childCount android.view.ViewGroup  
getChildAt android.view.ViewGroup  getLocationOnScreen android.view.ViewGroup  height android.view.ViewGroup  let android.view.ViewGroup  
removeView android.view.ViewGroup  
requestLayout android.view.ViewGroup  width android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  bottomMargin )android.view.ViewGroup.MarginLayoutParams  
leftMargin )android.view.ViewGroup.MarginLayoutParams  rightMargin )android.view.ViewGroup.MarginLayoutParams  setDuration !android.view.ViewPropertyAnimator  setListener !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  translationY !android.view.ViewPropertyAnimator  defaultDisplay android.view.WindowManager  DecelerateInterpolator android.view.animation  LinearLayout android.widget  RelativeLayout android.widget  TextView android.widget  
GRAVITY_START android.widget.FrameLayout  MODE_SCROLLABLE android.widget.FrameLayout  OnTabSelectedListener android.widget.FrameLayout  Tab android.widget.FrameLayout  android android.widget.FrameLayout  let android.widget.FrameLayout  
GRAVITY_START #android.widget.HorizontalScrollView  MODE_SCROLLABLE #android.widget.HorizontalScrollView  OnTabSelectedListener #android.widget.HorizontalScrollView  Tab #android.widget.HorizontalScrollView  let #android.widget.HorizontalScrollView  Gravity android.widget.LinearLayout  
HORIZONTAL android.widget.LinearLayout  LAYER_TYPE_SOFTWARE android.widget.LinearLayout  LayoutParams android.widget.LinearLayout  LinearLayout android.widget.LinearLayout  Paint android.widget.LinearLayout  Path android.widget.LinearLayout  R android.widget.LinearLayout  RectF android.widget.LinearLayout  addView android.widget.LinearLayout  animate android.widget.LinearLayout  apply android.widget.LinearLayout  calculateOptimalElevation android.widget.LinearLayout  config android.widget.LinearLayout  	elevation android.widget.LinearLayout  gravity android.widget.LinearLayout  height android.widget.LinearLayout  let android.widget.LinearLayout  onDraw android.widget.LinearLayout  orientation android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  translationY android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  text android.widget.TextView  CoordinatorLayout !androidx.coordinatorlayout.widget  LayoutParams 3androidx.coordinatorlayout.widget.CoordinatorLayout  addView 3androidx.coordinatorlayout.widget.CoordinatorLayout  
childCount 3androidx.coordinatorlayout.widget.CoordinatorLayout  
getChildAt 3androidx.coordinatorlayout.widget.CoordinatorLayout  post 3androidx.coordinatorlayout.widget.CoordinatorLayout  postDelayed 3androidx.coordinatorlayout.widget.CoordinatorLayout  
removeView 3androidx.coordinatorlayout.widget.CoordinatorLayout  AppBarLayout <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  R <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  android <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  until <androidx.coordinatorlayout.widget.CoordinatorLayout.Behavior  LayoutParams Iandroidx.coordinatorlayout.widget.CoordinatorLayout.Behavior.AppBarLayout  Gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  MATCH_PARENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  WRAP_CONTENT @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  actualBottomMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  apply @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  behavior @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  bottomMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  config @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  gravity @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  
leftMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  rightMargin @androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams  NestedScrollView androidx.core.widget  android %androidx.core.widget.NestedScrollView  onNestedScroll %androidx.core.widget.NestedScrollView  onScrollChanged %androidx.core.widget.NestedScrollView  Fragment androidx.fragment.app  ARG_POSITION androidx.fragment.app.Fragment  
ARG_TAB_TITLE androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  ContentFragment androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  onCreate androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  
ViewPager2 androidx.viewpager2.widget  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  let %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  unregisterOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  	TabLayout :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  isBottomTabVisible :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  let :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  AppBarLayout "com.google.android.material.appbar  Behavior /com.google.android.material.appbar.AppBarLayout  LayoutParams /com.google.android.material.appbar.AppBarLayout  OnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  addOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  
childCount /com.google.android.material.appbar.AppBarLayout  
getChildAt /com.google.android.material.appbar.AppBarLayout  layoutParams /com.google.android.material.appbar.AppBarLayout  removeOnOffsetChangedListener /com.google.android.material.appbar.AppBarLayout  
requestLayout /com.google.android.material.appbar.AppBarLayout  setExpanded /com.google.android.material.appbar.AppBarLayout  totalScrollRange /com.google.android.material.appbar.AppBarLayout  AppBarLayout <com.google.android.material.appbar.AppBarLayout.BaseBehavior  R <com.google.android.material.appbar.AppBarLayout.BaseBehavior  android <com.google.android.material.appbar.AppBarLayout.BaseBehavior  until <com.google.android.material.appbar.AppBarLayout.BaseBehavior  LayoutParams Icom.google.android.material.appbar.AppBarLayout.BaseBehavior.AppBarLayout  AppBarLayout 8com.google.android.material.appbar.AppBarLayout.Behavior  R 8com.google.android.material.appbar.AppBarLayout.Behavior  android 8com.google.android.material.appbar.AppBarLayout.Behavior  
onLayoutChild 8com.google.android.material.appbar.AppBarLayout.Behavior  onNestedScroll 8com.google.android.material.appbar.AppBarLayout.Behavior  topAndBottomOffset 8com.google.android.material.appbar.AppBarLayout.Behavior  until 8com.google.android.material.appbar.AppBarLayout.Behavior  LayoutParams Ecom.google.android.material.appbar.AppBarLayout.Behavior.AppBarLayout  SCROLL_FLAG_ENTER_ALWAYS <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SCROLL <com.google.android.material.appbar.AppBarLayout.LayoutParams  SCROLL_FLAG_SNAP <com.google.android.material.appbar.AppBarLayout.LayoutParams  scrollFlags <com.google.android.material.appbar.AppBarLayout.LayoutParams  let Gcom.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener  AppBarLayout 1com.google.android.material.appbar.HeaderBehavior  R 1com.google.android.material.appbar.HeaderBehavior  android 1com.google.android.material.appbar.HeaderBehavior  until 1com.google.android.material.appbar.HeaderBehavior  LayoutParams >com.google.android.material.appbar.HeaderBehavior.AppBarLayout  AppBarLayout 5com.google.android.material.appbar.ViewOffsetBehavior  R 5com.google.android.material.appbar.ViewOffsetBehavior  android 5com.google.android.material.appbar.ViewOffsetBehavior  topAndBottomOffset 5com.google.android.material.appbar.ViewOffsetBehavior  until 5com.google.android.material.appbar.ViewOffsetBehavior  LayoutParams Bcom.google.android.material.appbar.ViewOffsetBehavior.AppBarLayout  	TabLayout  com.google.android.material.tabs  
GRAVITY_START *com.google.android.material.tabs.TabLayout  LinearLayout *com.google.android.material.tabs.TabLayout  MODE_SCROLLABLE *com.google.android.material.tabs.TabLayout  OnTabSelectedListener *com.google.android.material.tabs.TabLayout  Tab *com.google.android.material.tabs.TabLayout  addOnTabSelectedListener *com.google.android.material.tabs.TabLayout  addTab *com.google.android.material.tabs.TabLayout  apply *com.google.android.material.tabs.TabLayout  getTabAt *com.google.android.material.tabs.TabLayout  layoutParams *com.google.android.material.tabs.TabLayout  let *com.google.android.material.tabs.TabLayout  newTab *com.google.android.material.tabs.TabLayout  selectedTabPosition *com.google.android.material.tabs.TabLayout  setOnTabSelectedListener *com.google.android.material.tabs.TabLayout  tabCount *com.google.android.material.tabs.TabLayout  
tabGravity *com.google.android.material.tabs.TabLayout  tabMode *com.google.android.material.tabs.TabLayout  
onTabSelected @com.google.android.material.tabs.TabLayout.OnTabSelectedListener  icon .com.google.android.material.tabs.TabLayout.Tab  let .com.google.android.material.tabs.TabLayout.Tab  position .com.google.android.material.tabs.TabLayout.Tab  select .com.google.android.material.tabs.TabLayout.Tab  text .com.google.android.material.tabs.TabLayout.Tab  AttributeSet com.hbg.lib.widgets  Context com.hbg.lib.widgets  Int com.hbg.lib.widgets  JvmOverloads com.hbg.lib.widgets  LoadingRelativeLayout com.hbg.lib.widgets  RelativeLayout com.hbg.lib.widgets  AppBarLayout !com.hbg.module.libkt.custom.coord  AppBarLayoutBehavior !com.hbg.module.libkt.custom.coord  AttributeSet !com.hbg.module.libkt.custom.coord  Boolean !com.hbg.module.libkt.custom.coord  Context !com.hbg.module.libkt.custom.coord  CoordinatorLayout !com.hbg.module.libkt.custom.coord  Int !com.hbg.module.libkt.custom.coord  IntArray !com.hbg.module.libkt.custom.coord  R !com.hbg.module.libkt.custom.coord  View !com.hbg.module.libkt.custom.coord  android !com.hbg.module.libkt.custom.coord  until !com.hbg.module.libkt.custom.coord  Behavior .com.hbg.module.libkt.custom.coord.AppBarLayout  LayoutParams .com.hbg.module.libkt.custom.coord.AppBarLayout  AppBarLayout 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  R 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  android 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  handleStickyViewsOnScroll 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  isStickyView 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  processStickyViews 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  setupStickyBehavior 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  until 6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior  AttributeSet %com.hbg.module.libkt.custom.indicator  CoIndicator %com.hbg.module.libkt.custom.indicator  Context %com.hbg.module.libkt.custom.indicator  
GRAVITY_START %com.hbg.module.libkt.custom.indicator  Int %com.hbg.module.libkt.custom.indicator  JvmOverloads %com.hbg.module.libkt.custom.indicator  MODE_SCROLLABLE %com.hbg.module.libkt.custom.indicator  OnTabSelectedListener %com.hbg.module.libkt.custom.indicator  String %com.hbg.module.libkt.custom.indicator  Tab %com.hbg.module.libkt.custom.indicator  	TabLayout %com.hbg.module.libkt.custom.indicator  Unit %com.hbg.module.libkt.custom.indicator  let %com.hbg.module.libkt.custom.indicator  
GRAVITY_START 1com.hbg.module.libkt.custom.indicator.CoIndicator  LinearLayout 1com.hbg.module.libkt.custom.indicator.CoIndicator  MODE_SCROLLABLE 1com.hbg.module.libkt.custom.indicator.CoIndicator  addOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  addTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  apply 1com.hbg.module.libkt.custom.indicator.CoIndicator  getTabAt 1com.hbg.module.libkt.custom.indicator.CoIndicator  	javaClass 1com.hbg.module.libkt.custom.indicator.CoIndicator  layoutParams 1com.hbg.module.libkt.custom.indicator.CoIndicator  let 1com.hbg.module.libkt.custom.indicator.CoIndicator  newTab 1com.hbg.module.libkt.custom.indicator.CoIndicator  selectedTabPosition 1com.hbg.module.libkt.custom.indicator.CoIndicator  setOnTabSelectedListener 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabCount 1com.hbg.module.libkt.custom.indicator.CoIndicator  
tabGravity 1com.hbg.module.libkt.custom.indicator.CoIndicator  tabMode 1com.hbg.module.libkt.custom.indicator.CoIndicator  Animator com.huobi.home.ui  AnimatorListenerAdapter com.huobi.home.ui  Any com.huobi.home.ui  AppBarLayout com.huobi.home.ui  	ArrayList com.huobi.home.ui  Boolean com.huobi.home.ui  Class com.huobi.home.ui  CoIndicator com.huobi.home.ui  CoIndicatorAware com.huobi.home.ui  CoIndicatorListenerAdapter com.huobi.home.ui  CoIndicatorListenerAware com.huobi.home.ui  Config com.huobi.home.ui  Context com.huobi.home.ui  CoordinatorLayout com.huobi.home.ui  	Exception com.huobi.home.ui  Float com.huobi.home.ui  Gravity com.huobi.home.ui  Int com.huobi.home.ui  IntArray com.huobi.home.ui  LinearLayout com.huobi.home.ui  List com.huobi.home.ui  Long com.huobi.home.ui  MutableList com.huobi.home.ui  StickyBottomTabPlugin com.huobi.home.ui  StickyBottomTabPluginFactory com.huobi.home.ui  String com.huobi.home.ui  Suppress com.huobi.home.ui  	TabLayout com.huobi.home.ui  Unit com.huobi.home.ui  	ViewGroup com.huobi.home.ui  
ViewPager2 com.huobi.home.ui  actualBottomMargin com.huobi.home.ui  android com.huobi.home.ui  apply com.huobi.home.ui  calculateOptimalElevation com.huobi.home.ui  com com.huobi.home.ui  config com.huobi.home.ui  contains com.huobi.home.ui  	emptyList com.huobi.home.ui  forEach com.huobi.home.ui  handleTabClick com.huobi.home.ui  hideStickyBottomTab com.huobi.home.ui  isAnimating com.huobi.home.ui  isBottomTabVisible com.huobi.home.ui  
isNotEmpty com.huobi.home.ui  java com.huobi.home.ui  	javaClass com.huobi.home.ui  joinToString com.huobi.home.ui  kotlin com.huobi.home.ui  let com.huobi.home.ui  	lowercase com.huobi.home.ui  	maxOrNull com.huobi.home.ui  
mutableListOf com.huobi.home.ui  requireNotNull com.huobi.home.ui  until com.huobi.home.ui  Behavior com.huobi.home.ui.AppBarLayout  OnOffsetChangedListener com.huobi.home.ui.AppBarLayout  LayoutParams #com.huobi.home.ui.CoordinatorLayout  Animator 'com.huobi.home.ui.StickyBottomTabPlugin  AnimatorListenerAdapter 'com.huobi.home.ui.StickyBottomTabPlugin  Any 'com.huobi.home.ui.StickyBottomTabPlugin  AppBarLayout 'com.huobi.home.ui.StickyBottomTabPlugin  	ArrayList 'com.huobi.home.ui.StickyBottomTabPlugin  Boolean 'com.huobi.home.ui.StickyBottomTabPlugin  Builder 'com.huobi.home.ui.StickyBottomTabPlugin  CoIndicatorListenerAdapter 'com.huobi.home.ui.StickyBottomTabPlugin  Config 'com.huobi.home.ui.StickyBottomTabPlugin  Context 'com.huobi.home.ui.StickyBottomTabPlugin  CoordinatorLayout 'com.huobi.home.ui.StickyBottomTabPlugin  	Exception 'com.huobi.home.ui.StickyBottomTabPlugin  Float 'com.huobi.home.ui.StickyBottomTabPlugin  Gravity 'com.huobi.home.ui.StickyBottomTabPlugin  Int 'com.huobi.home.ui.StickyBottomTabPlugin  IntArray 'com.huobi.home.ui.StickyBottomTabPlugin  LinearLayout 'com.huobi.home.ui.StickyBottomTabPlugin  List 'com.huobi.home.ui.StickyBottomTabPlugin  Long 'com.huobi.home.ui.StickyBottomTabPlugin  MutableList 'com.huobi.home.ui.StickyBottomTabPlugin  StickyBottomTabPlugin 'com.huobi.home.ui.StickyBottomTabPlugin  String 'com.huobi.home.ui.StickyBottomTabPlugin  Suppress 'com.huobi.home.ui.StickyBottomTabPlugin  	TabLayout 'com.huobi.home.ui.StickyBottomTabPlugin  Unit 'com.huobi.home.ui.StickyBottomTabPlugin  	ViewGroup 'com.huobi.home.ui.StickyBottomTabPlugin  
ViewPager2 'com.huobi.home.ui.StickyBottomTabPlugin  actualBottomMargin 'com.huobi.home.ui.StickyBottomTabPlugin  android 'com.huobi.home.ui.StickyBottomTabPlugin  apply 'com.huobi.home.ui.StickyBottomTabPlugin  calculateOptimalElevation 'com.huobi.home.ui.StickyBottomTabPlugin  checkBottomStickyTabVisibility 'com.huobi.home.ui.StickyBottomTabPlugin  coIndicatorListenerAdapter 'com.huobi.home.ui.StickyBottomTabPlugin  com 'com.huobi.home.ui.StickyBottomTabPlugin  config 'com.huobi.home.ui.StickyBottomTabPlugin  contains 'com.huobi.home.ui.StickyBottomTabPlugin  context 'com.huobi.home.ui.StickyBottomTabPlugin  createCoIndicatorCopy 'com.huobi.home.ui.StickyBottomTabPlugin  createGenericTabCopy 'com.huobi.home.ui.StickyBottomTabPlugin  createStickyTabContainer 'com.huobi.home.ui.StickyBottomTabPlugin  createStickyTabLayout 'com.huobi.home.ui.StickyBottomTabPlugin  createTabLayoutCopy 'com.huobi.home.ui.StickyBottomTabPlugin  "detectExistingNavigationElevations 'com.huobi.home.ui.StickyBottomTabPlugin  detectNavigationBars 'com.huobi.home.ui.StickyBottomTabPlugin   detectNavigationViewsRecursively 'com.huobi.home.ui.StickyBottomTabPlugin  disable 'com.huobi.home.ui.StickyBottomTabPlugin  	emptyList 'com.huobi.home.ui.StickyBottomTabPlugin  getNavigationBarHeight 'com.huobi.home.ui.StickyBottomTabPlugin  getStatusBarHeight 'com.huobi.home.ui.StickyBottomTabPlugin  handleTabClick 'com.huobi.home.ui.StickyBottomTabPlugin  hasNavigationBar 'com.huobi.home.ui.StickyBottomTabPlugin  hideStickyBottomTab 'com.huobi.home.ui.StickyBottomTabPlugin  isAnimating 'com.huobi.home.ui.StickyBottomTabPlugin  isBottomTabVisible 'com.huobi.home.ui.StickyBottomTabPlugin  	isEnabled 'com.huobi.home.ui.StickyBottomTabPlugin  
isInitialized 'com.huobi.home.ui.StickyBottomTabPlugin  isNavigationRelatedView 'com.huobi.home.ui.StickyBottomTabPlugin  
isNotEmpty 'com.huobi.home.ui.StickyBottomTabPlugin  java 'com.huobi.home.ui.StickyBottomTabPlugin  	javaClass 'com.huobi.home.ui.StickyBottomTabPlugin  joinToString 'com.huobi.home.ui.StickyBottomTabPlugin  kotlin 'com.huobi.home.ui.StickyBottomTabPlugin  let 'com.huobi.home.ui.StickyBottomTabPlugin  	lowercase 'com.huobi.home.ui.StickyBottomTabPlugin  	maxOrNull 'com.huobi.home.ui.StickyBottomTabPlugin  
mutableListOf 'com.huobi.home.ui.StickyBottomTabPlugin  navigationBarHeight 'com.huobi.home.ui.StickyBottomTabPlugin  offsetChangeListener 'com.huobi.home.ui.StickyBottomTabPlugin  originalCoIndicator 'com.huobi.home.ui.StickyBottomTabPlugin  pageChangeCallback 'com.huobi.home.ui.StickyBottomTabPlugin  performScrollToPosition 'com.huobi.home.ui.StickyBottomTabPlugin  removeScrollListener 'com.huobi.home.ui.StickyBottomTabPlugin  requireNotNull 'com.huobi.home.ui.StickyBottomTabPlugin  setCoIndicatorAdapter 'com.huobi.home.ui.StickyBottomTabPlugin  setTabTitles 'com.huobi.home.ui.StickyBottomTabPlugin  setupScrollListener 'com.huobi.home.ui.StickyBottomTabPlugin  setupTabSync 'com.huobi.home.ui.StickyBottomTabPlugin  showInitialStickyBottomTab 'com.huobi.home.ui.StickyBottomTabPlugin  showStickyBottomTab 'com.huobi.home.ui.StickyBottomTabPlugin  #showStickyBottomTabWithoutAnimation 'com.huobi.home.ui.StickyBottomTabPlugin  statusBarHeight 'com.huobi.home.ui.StickyBottomTabPlugin  stickyBottomTabContainer 'com.huobi.home.ui.StickyBottomTabPlugin  	tabTitles 'com.huobi.home.ui.StickyBottomTabPlugin  until 'com.huobi.home.ui.StickyBottomTabPlugin  Behavior 4com.huobi.home.ui.StickyBottomTabPlugin.AppBarLayout  OnOffsetChangedListener 4com.huobi.home.ui.StickyBottomTabPlugin.AppBarLayout  Config /com.huobi.home.ui.StickyBottomTabPlugin.Builder  StickyBottomTabPlugin /com.huobi.home.ui.StickyBottomTabPlugin.Builder  animationDuration /com.huobi.home.ui.StickyBottomTabPlugin.Builder  appBarLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  apply /com.huobi.home.ui.StickyBottomTabPlugin.Builder  autoDetectNavigationBar /com.huobi.home.ui.StickyBottomTabPlugin.Builder  bottomMargin /com.huobi.home.ui.StickyBottomTabPlugin.Builder  build /com.huobi.home.ui.StickyBottomTabPlugin.Builder  context /com.huobi.home.ui.StickyBottomTabPlugin.Builder  coordinatorLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  horizontalMargin /com.huobi.home.ui.StickyBottomTabPlugin.Builder  maxElevation /com.huobi.home.ui.StickyBottomTabPlugin.Builder  onTabClickListener /com.huobi.home.ui.StickyBottomTabPlugin.Builder  originalTabLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  requireNotNull /com.huobi.home.ui.StickyBottomTabPlugin.Builder  scrollToPosition /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setAnimationDuration /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setAppBarLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setAutoDetectNavigationBar /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setBottomMargin /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setCoordinatorLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setHorizontalMargin /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setMaxElevation /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setOnTabClickListener /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setOriginalTabLayout /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setScrollToPosition /com.huobi.home.ui.StickyBottomTabPlugin.Builder  setViewPager /com.huobi.home.ui.StickyBottomTabPlugin.Builder  tabBackgroundColor /com.huobi.home.ui.StickyBottomTabPlugin.Builder  tabElevation /com.huobi.home.ui.StickyBottomTabPlugin.Builder  	viewPager /com.huobi.home.ui.StickyBottomTabPlugin.Builder  Int Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  	TabLayout Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  android Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  captureIndicatorHelperListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  captureOriginalListeners Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  capturePageSelectListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  captureTabSelectedListeners Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  coIndicator Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  destroy Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  indicatorHelperListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  java Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  	javaClass Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  let Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  
mutableListOf Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  originalPageSelectListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  originalTabSelectedListeners Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  triggerAllListeners Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  triggerIndicatorHelperListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  triggerPageSelectListener Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  triggerTabSelectedListeners Bcom.huobi.home.ui.StickyBottomTabPlugin.CoIndicatorListenerAdapter  animationDuration .com.huobi.home.ui.StickyBottomTabPlugin.Config  appBarLayout .com.huobi.home.ui.StickyBottomTabPlugin.Config  autoDetectNavigationBar .com.huobi.home.ui.StickyBottomTabPlugin.Config  bottomMargin .com.huobi.home.ui.StickyBottomTabPlugin.Config  coordinatorLayout .com.huobi.home.ui.StickyBottomTabPlugin.Config  horizontalMargin .com.huobi.home.ui.StickyBottomTabPlugin.Config  maxElevation .com.huobi.home.ui.StickyBottomTabPlugin.Config  onTabClickListener .com.huobi.home.ui.StickyBottomTabPlugin.Config  originalTabLayout .com.huobi.home.ui.StickyBottomTabPlugin.Config  scrollToPosition .com.huobi.home.ui.StickyBottomTabPlugin.Config  tabBackgroundColor .com.huobi.home.ui.StickyBottomTabPlugin.Config  tabElevation .com.huobi.home.ui.StickyBottomTabPlugin.Config  	viewPager .com.huobi.home.ui.StickyBottomTabPlugin.Config  LayoutParams 9com.huobi.home.ui.StickyBottomTabPlugin.CoordinatorLayout  OnTabSelectedListener 1com.huobi.home.ui.StickyBottomTabPlugin.TabLayout  Tab 1com.huobi.home.ui.StickyBottomTabPlugin.TabLayout  OnPageChangeCallback 2com.huobi.home.ui.StickyBottomTabPlugin.ViewPager2  	animation /com.huobi.home.ui.StickyBottomTabPlugin.android  app /com.huobi.home.ui.StickyBottomTabPlugin.android  view /com.huobi.home.ui.StickyBottomTabPlugin.android  Animator 9com.huobi.home.ui.StickyBottomTabPlugin.android.animation  AnimatorListenerAdapter 9com.huobi.home.ui.StickyBottomTabPlugin.android.animation  Activity 3com.huobi.home.ui.StickyBottomTabPlugin.android.app  View 4com.huobi.home.ui.StickyBottomTabPlugin.android.view  	ViewGroup 4com.huobi.home.ui.StickyBottomTabPlugin.android.view  hbg +com.huobi.home.ui.StickyBottomTabPlugin.com  module /com.huobi.home.ui.StickyBottomTabPlugin.com.hbg  libkt 6com.huobi.home.ui.StickyBottomTabPlugin.com.hbg.module  custom <com.huobi.home.ui.StickyBottomTabPlugin.com.hbg.module.libkt  	indicator Ccom.huobi.home.ui.StickyBottomTabPlugin.com.hbg.module.libkt.custom  CoIndicator Mcom.huobi.home.ui.StickyBottomTabPlugin.com.hbg.module.libkt.custom.indicator  AppBarLayout .com.huobi.home.ui.StickyBottomTabPluginFactory  Boolean .com.huobi.home.ui.StickyBottomTabPluginFactory  Class .com.huobi.home.ui.StickyBottomTabPluginFactory  CoIndicator .com.huobi.home.ui.StickyBottomTabPluginFactory  CoIndicatorAware .com.huobi.home.ui.StickyBottomTabPluginFactory  CoIndicatorListenerAware .com.huobi.home.ui.StickyBottomTabPluginFactory  Context .com.huobi.home.ui.StickyBottomTabPluginFactory  CoordinatorLayout .com.huobi.home.ui.StickyBottomTabPluginFactory  	Exception .com.huobi.home.ui.StickyBottomTabPluginFactory  Float .com.huobi.home.ui.StickyBottomTabPluginFactory  Int .com.huobi.home.ui.StickyBottomTabPluginFactory  List .com.huobi.home.ui.StickyBottomTabPluginFactory  Long .com.huobi.home.ui.StickyBottomTabPluginFactory  StickyBottomTabPlugin .com.huobi.home.ui.StickyBottomTabPluginFactory  String .com.huobi.home.ui.StickyBottomTabPluginFactory  
ViewPager2 .com.huobi.home.ui.StickyBottomTabPluginFactory  android .com.huobi.home.ui.StickyBottomTabPluginFactory  apply .com.huobi.home.ui.StickyBottomTabPluginFactory  handleCoIndicatorClick .com.huobi.home.ui.StickyBottomTabPluginFactory  java .com.huobi.home.ui.StickyBottomTabPluginFactory  	javaClass .com.huobi.home.ui.StickyBottomTabPluginFactory  let .com.huobi.home.ui.StickyBottomTabPluginFactory  triggerIndicatorHelperListeners .com.huobi.home.ui.StickyBottomTabPluginFactory  triggerOriginalCoIndicatorClick .com.huobi.home.ui.StickyBottomTabPluginFactory  triggerPageSelectListener .com.huobi.home.ui.StickyBottomTabPluginFactory  OnTabSelectedListener com.huobi.home.ui.TabLayout  Tab com.huobi.home.ui.TabLayout  OnPageChangeCallback com.huobi.home.ui.ViewPager2  	animation com.huobi.home.ui.android  app com.huobi.home.ui.android  view com.huobi.home.ui.android  Animator #com.huobi.home.ui.android.animation  AnimatorListenerAdapter #com.huobi.home.ui.android.animation  Activity com.huobi.home.ui.android.app  View com.huobi.home.ui.android.view  	ViewGroup com.huobi.home.ui.android.view  hbg com.huobi.home.ui.com  module com.huobi.home.ui.com.hbg  libkt  com.huobi.home.ui.com.hbg.module  custom &com.huobi.home.ui.com.hbg.module.libkt  	indicator -com.huobi.home.ui.com.hbg.module.libkt.custom  CoIndicator 7com.huobi.home.ui.com.hbg.module.libkt.custom.indicator  AttributeSet com.huobi.view  Context com.huobi.view  Int com.huobi.view  JvmOverloads com.huobi.view  MyNestedScrollView com.huobi.view  NestedScrollView com.huobi.view  android com.huobi.view  android !com.huobi.view.MyNestedScrollView  scrollY !com.huobi.view.MyNestedScrollView  view com.huobi.view.android  View com.huobi.view.android.view  AttributeSet com.huobi.view.roundview  Canvas com.huobi.view.roundview  Context com.huobi.view.roundview  Int com.huobi.view.roundview  JvmOverloads com.huobi.view.roundview  LAYER_TYPE_SOFTWARE com.huobi.view.roundview  LinearLayout com.huobi.view.roundview  Paint com.huobi.view.roundview  Path com.huobi.view.roundview  R com.huobi.view.roundview  RectF com.huobi.view.roundview  RoundLinearLayout com.huobi.view.roundview  let com.huobi.view.roundview  LAYER_TYPE_SOFTWARE *com.huobi.view.roundview.RoundLinearLayout  Paint *com.huobi.view.roundview.RoundLinearLayout  Path *com.huobi.view.roundview.RoundLinearLayout  R *com.huobi.view.roundview.RoundLinearLayout  RectF *com.huobi.view.roundview.RoundLinearLayout  backgroundColor *com.huobi.view.roundview.RoundLinearLayout  cornerRadius *com.huobi.view.roundview.RoundLinearLayout  height *com.huobi.view.roundview.RoundLinearLayout  isRippleEnable *com.huobi.view.roundview.RoundLinearLayout  let *com.huobi.view.roundview.RoundLinearLayout  paint *com.huobi.view.roundview.RoundLinearLayout  path *com.huobi.view.roundview.RoundLinearLayout  rectF *com.huobi.view.roundview.RoundLinearLayout  setLayerType *com.huobi.view.roundview.RoundLinearLayout  width *com.huobi.view.roundview.RoundLinearLayout  ARG_POSITION com.ttv.demo  
ARG_TAB_TITLE com.ttv.demo  Bundle com.ttv.demo  ContentFragment com.ttv.demo  Fragment com.ttv.demo  Int com.ttv.demo  LayoutInflater com.ttv.demo  R com.ttv.demo  String com.ttv.demo  TextView com.ttv.demo  View com.ttv.demo  	ViewGroup com.ttv.demo  let com.ttv.demo  ARG_POSITION com.ttv.demo.ContentFragment  
ARG_TAB_TITLE com.ttv.demo.ContentFragment  Bundle com.ttv.demo.ContentFragment  ContentFragment com.ttv.demo.ContentFragment  Int com.ttv.demo.ContentFragment  LayoutInflater com.ttv.demo.ContentFragment  R com.ttv.demo.ContentFragment  String com.ttv.demo.ContentFragment  TextView com.ttv.demo.ContentFragment  View com.ttv.demo.ContentFragment  	ViewGroup com.ttv.demo.ContentFragment  	arguments com.ttv.demo.ContentFragment  let com.ttv.demo.ContentFragment  position com.ttv.demo.ContentFragment  setupContentForTab com.ttv.demo.ContentFragment  tabTitle com.ttv.demo.ContentFragment  ARG_POSITION &com.ttv.demo.ContentFragment.Companion  
ARG_TAB_TITLE &com.ttv.demo.ContentFragment.Companion  Bundle &com.ttv.demo.ContentFragment.Companion  ContentFragment &com.ttv.demo.ContentFragment.Companion  R &com.ttv.demo.ContentFragment.Companion  let &com.ttv.demo.ContentFragment.Companion  home_feed_linear_tabLayout com.ttv.demo.R.id  tv_tab_title com.ttv.demo.R.id  fragment_content com.ttv.demo.R.layout  RoundLinearLayout com.ttv.demo.R.styleable  $RoundLinearLayout_rv_backgroundColor com.ttv.demo.R.styleable  !RoundLinearLayout_rv_cornerRadius com.ttv.demo.R.styleable  #RoundLinearLayout_rv_isRippleEnable com.ttv.demo.R.styleable  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  forName java.lang.Class  getDeclaredField java.lang.Class  getDeclaredMethod java.lang.Class  
simpleName java.lang.Class  <SAM-CONSTRUCTOR> java.lang.Runnable  isAccessible "java.lang.reflect.AccessibleObject  get java.lang.reflect.Field  invoke java.lang.reflect.Method  	ArrayList 	java.util  let java.util.ArrayList  size java.util.ArrayList  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Suppress kotlin  apply kotlin  let kotlin  requireNotNull kotlin  	javaClass 
kotlin.Any  let 
kotlin.Any  not kotlin.Boolean  	compareTo kotlin.Float  plus kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	Companion 
kotlin.Int  	compareTo 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  
unaryMinus 
kotlin.Int  get kotlin.IntArray  toInt kotlin.Long  contains 
kotlin.String  plus 
kotlin.String  IntIterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  	maxOrNull kotlin.collections  
mutableListOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
isNotEmpty kotlin.collections.List  joinToString kotlin.collections.List  let kotlin.collections.List  	maxOrNull kotlin.collections.List  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  clear kotlin.collections.MutableList  size kotlin.collections.MutableList  JvmOverloads 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  kotlin 
kotlin.jvm  min kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  java kotlin.reflect.KClass  contains kotlin.sequences  forEach kotlin.sequences  joinToString kotlin.sequences  	maxOrNull kotlin.sequences  contains kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  	lowercase kotlin.text  	maxOrNull kotlin.text  Insets android.graphics  Rect android.graphics  bottom android.graphics.Insets  left android.graphics.Insets  right android.graphics.Insets  Build 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  WindowInsets android.view  
WindowMetrics android.view  	getInsets android.view.WindowInsets  navigationBars android.view.WindowInsets.Type  currentWindowMetrics android.view.WindowManager  bounds android.view.WindowMetrics  windowInsets android.view.WindowMetrics  Build com.huobi.home.ui  
WindowMetrics com.huobi.home.ui  Build 'com.huobi.home.ui.StickyBottomTabPlugin  
WindowMetrics 'com.huobi.home.ui.StickyBottomTabPlugin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         