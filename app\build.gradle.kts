plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.ttv.demo"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.ttv.demo"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)

    // ViewPager2
    implementation("androidx.viewpager2:viewpager2:1.1.0")

    // Fragment
    implementation("androidx.fragment:fragment-ktx:1.8.5")

    // SmartRefreshLayout - 3.0版本官方依赖配置
    implementation("io.github.scwang90:refresh-layout-kernel:3.0.0-alpha")      // 核心必须依赖
    implementation("io.github.scwang90:refresh-header-classics:3.0.0-alpha")    // 经典刷新头
    implementation("io.github.scwang90:refresh-footer-classics:3.0.0-alpha")    // 经典加载

    // RecyclerView
    implementation("androidx.recyclerview:recyclerview:1.3.2")

    // ConstraintLayout for better layout management
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")

    // CardView
    implementation("androidx.cardview:cardview:1.0.0")

    // ConsecutiveScroller - 用于实现吸底效果
    implementation("com.github.donkingliang:ConsecutiveScroller:4.6.4")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}