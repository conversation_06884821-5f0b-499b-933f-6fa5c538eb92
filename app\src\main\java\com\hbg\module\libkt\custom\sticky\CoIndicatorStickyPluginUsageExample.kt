package com.hbg.module.libkt.custom.sticky

import android.content.Context
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.indicator.IndicatorHelper
import com.hbg.module.libkt.custom.indicator.TabClickListener

/**
 * CoIndicator吸底Tab插件使用示例
 * 展示如何正确集成插件以保留客户的自定义监听器
 */
class CoIndicatorStickyPluginUsageExample {

    /**
     * 标准使用方式 - 保留所有原始监听器
     * 这是推荐的使用方式，确保客户的所有业务逻辑都能正常工作
     */
    fun createPluginWithOriginalListeners(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabs: List<String>,
        // 客户的业务逻辑变量
        nowCurrentContentPos: Int,
        onRefreshData: (actionType: Int) -> Unit,
        onDebugStatus: (message: String) -> Unit
    ): StickyBottomTabPlugin {

        // 1. 首先设置原始CoIndicator的监听器（客户现有代码）
        setupOriginalCoIndicatorListeners(
            context, coIndicator, viewPager, tabs,
            nowCurrentContentPos, onRefreshData, onDebugStatus
        )

        // 2. 创建插件，启用原始监听器保留功能
        val plugin = StickyBottomTabPluginFactory.createForCoIndicator(
            context = context,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabs,
            animationDuration = 300L,
            scrollToPosition = 0.33f,
            autoDetectNavigationBar = true,
            bottomMargin = 100,
            maxElevation = 20f,
            preserveOriginalListeners = true  // 关键参数：保留原始监听器
        )

        // 3. 启用插件
        plugin.enable()

        return plugin
    }

    /**
     * 简化使用方式 - 仅基础功能
     * 适用于不需要复杂业务逻辑的场景
     */
    fun createSimplePlugin(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabs: List<String>
    ): StickyBottomTabPlugin {

        val plugin = StickyBottomTabPluginFactory.createForCoIndicator(
            context = context,
            coordinatorLayout = coordinatorLayout,
            appBarLayout = appBarLayout,
            coIndicator = coIndicator,
            viewPager = viewPager,
            tabTitles = tabs,
            preserveOriginalListeners = false  // 不保留原始监听器，仅基础功能
        )

        plugin.enable()
        return plugin
    }

    /**
     * 设置原始CoIndicator的监听器
     * 模拟客户现有的监听器设置代码
     */
    private fun setupOriginalCoIndicatorListeners(
        context: Context,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabs: List<String>,
        nowCurrentContentPos: Int,
        onRefreshData: (actionType: Int) -> Unit,
        onDebugStatus: (message: String) -> Unit
    ) {
        var currentPos = nowCurrentContentPos

        // 1. 设置IndicatorHelper监听器（客户现有代码）
        IndicatorHelper.initBottomNoLineIndicator(
            context,
            tabs.map { TabData(it, 0) }, // 假设TabData结构
            coIndicator,
            0f,
            viewPager,
            16f,
            android.R.attr.textColorPrimary,
            android.R.attr.textColorSecondary,
            scaleSize = 20f,
            isBold = true,
            onTabClick = object : TabClickListener {
                override fun onTabClick(index: Int) {
                    android.util.Log.d("CoIndicatorExample", "Tab点击事件: 索引=$index, 标题=${tabs.getOrNull(index)}")
                    
                    // 重复点击同一Tab的刷新逻辑
                    if (currentPos != -1 && currentPos == index) {
                        android.util.Log.d("CoIndicatorExample", "重复点击同一Tab，刷新数据")
                        onRefreshData(1) // ACTION_REFRESH
                    }
                    currentPos = index
                    onDebugStatus("Tab点击-索引$index")
                }
            }
        )

        // 2. 设置CoIndicator的PageSelectListener（客户现有代码）
        coIndicator.listener = object : CoIndicator.PageSelectListener {
            override fun onSelected(position: Int) {
                val item = tabs.getOrNull(position)
                // 埋点统计逻辑
                when (position) {
                    0 -> {
                        // SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
                        android.util.Log.d("CoIndicatorExample", "埋点: 发现Tab")
                    }
                    1 -> {
                        // SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
                        android.util.Log.d("CoIndicatorExample", "埋点: KX Tab")
                    }
                    2 -> {
                        // SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
                        android.util.Log.d("CoIndicatorExample", "埋点: 直播广场")
                    }
                    3 -> {
                        // SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
                        android.util.Log.d("CoIndicatorExample", "埋点: 公告Tab")
                        // changeRedPoint(4, false)
                    }
                }
            }
        }
    }

    /**
     * 完整的集成示例
     * 展示如何在Fragment中集成插件
     */
    fun integrateInFragment(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2
    ) {
        // 模拟客户的数据
        val tabs = listOf("发现", "KX", "直播", "公告")
        var nowCurrentContentPos = -1
        var stickyBottomTabPlugin: StickyBottomTabPlugin? = null

        // 刷新数据的回调
        val refreshDataCallback = { actionType: Int ->
            android.util.Log.d("CoIndicatorExample", "刷新数据: actionType=$actionType")
            // 客户的刷新逻辑
        }

        // 调试状态的回调
        val debugStatusCallback = { message: String ->
            android.util.Log.d("CoIndicatorExample", "调试状态: $message")
        }

        try {
            // 创建插件
            stickyBottomTabPlugin = createPluginWithOriginalListeners(
                context = context,
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                coIndicator = coIndicator,
                viewPager = viewPager,
                tabs = tabs,
                nowCurrentContentPos = nowCurrentContentPos,
                onRefreshData = refreshDataCallback,
                onDebugStatus = debugStatusCallback
            )

            android.util.Log.d("CoIndicatorExample", "插件集成成功")

        } catch (e: Exception) {
            android.util.Log.e("CoIndicatorExample", "插件集成失败", e)
        }

        // 在Fragment的onDestroyView中销毁插件
        // stickyBottomTabPlugin?.destroy()
    }
}

/**
 * 模拟TabData类（如果不存在的话）
 */
data class TabData(
    val name: String,
    val type: Int
)
