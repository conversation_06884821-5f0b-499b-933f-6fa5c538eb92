/ Header Record For PersistentHashMapValueStorage android.widget.RelativeLayout9 8com.google.android.material.appbar.AppBarLayout.Behavior+ *com.google.android.material.tabs.TabLayout9 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin& %androidx.core.widget.NestedScrollView android.widget.LinearLayout androidx.fragment.app.Fragment) (androidx.appcompat.app.AppCompatActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter) (androidx.appcompat.app.AppCompatActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter) (androidx.appcompat.app.AppCompatActivity1 0androidx.viewpager2.adapter.FragmentStateAdapter9 8com.hbg.module.libkt.custom.sticky.StickyBottomTabPlugin& %androidx.core.widget.NestedScrollView1 0androidx.viewpager2.adapter.FragmentStateAdapter androidx.fragment.app.Fragment9 8com.google.android.material.appbar.AppBarLayout.Behavior androidx.fragment.app.Fragment) (androidx.appcompat.app.AppCompatActivity