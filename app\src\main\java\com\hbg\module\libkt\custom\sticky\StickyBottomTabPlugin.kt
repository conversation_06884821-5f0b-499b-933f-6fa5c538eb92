package com.huobi.home.ui

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.content.Context
import android.view.Gravity
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.ttv.demo.R

open class StickyBottomTabPlugin protected constructor(
    protected val context: Context,
    protected val config: Config
) : StickyBottomTabPluginFactory.CoIndicatorAware, StickyBottomTabPluginFactory.CoIndicatorListenerAware {

    // 插件状态
    private var isEnabled = false
    private var isBottomTabVisible = false
    private var isAnimating = false
    private var isInitialized = false  // 添加初始化标志
    private var stickyBottomTabContainer: LinearLayout? = null

    // 滚动监听器
    private var offsetChangeListener: AppBarLayout.OnOffsetChangedListener? = null
    private var pageChangeCallback: ViewPager2.OnPageChangeCallback? = null

    // 导航栏适配相关
    private var navigationBarHeight: Int = 0
    private var statusBarHeight: Int = 0
    private var actualBottomMargin: Int = 0

    // CoIndicator适配相关
    private var originalCoIndicator: com.hbg.module.libkt.custom.indicator.CoIndicator? = null
    private var tabTitles: List<String?> = emptyList()
    private var coIndicatorListenerAdapter: CoIndicatorListenerAdapter? = null


    /**
     * 配置类 - 封装所有可配置参数
     */
    data class Config(
        val coordinatorLayout: CoordinatorLayout,
        val appBarLayout: AppBarLayout,
        val originalTabLayout: ViewGroup,
        val viewPager: ViewPager2?,
        val animationDuration: Long = 300L,
        val scrollToPosition: Float = 0.33f, // 滚动到屏幕的1/3位置
        val tabBackgroundColor: Int = 0xFFFFFFFF.toInt(),
        val tabElevation: Float = 8f,
        val onTabClickListener: ((position: Int) -> Unit)? = null,
        // 新增导航栏适配配置
        val bottomMargin: Int = 0, // 底部边距，用于避开底部导航栏
        val autoDetectNavigationBar: Boolean = true, // 是否自动检测导航栏
        val maxElevation: Float = 16f, // 最大elevation值，确保在所有导航栏之上
        val horizontalMargin: Int = 0 // 水平边距，用于避开侧边导航栏
    )

    /**
     * 建造者模式 - 提供简洁的API接口
     */
    class Builder(private val context: Context) {
        private var coordinatorLayout: CoordinatorLayout? = null
        private var appBarLayout: AppBarLayout? = null
        private var originalTabLayout: ViewGroup? = null
        private var viewPager: ViewPager2? = null
        private var animationDuration: Long = 300L
        private var scrollToPosition: Float = 0.33f
        private var tabBackgroundColor: Int = 0xFFFFFFFF.toInt()
        private var tabElevation: Float = 8f
        private var onTabClickListener: ((position: Int) -> Unit)? = null
        // 新增导航栏适配参数
        private var bottomMargin: Int = 100
        private var autoDetectNavigationBar: Boolean = true
        private var maxElevation: Float = 16f
        private var horizontalMargin: Int = 0

        fun setCoordinatorLayout(coordinatorLayout: CoordinatorLayout) = apply {
            this.coordinatorLayout = coordinatorLayout
        }

        fun setAppBarLayout(appBarLayout: AppBarLayout) = apply {
            this.appBarLayout = appBarLayout
        }

        fun setOriginalTabLayout(tabLayout: ViewGroup) = apply {
            this.originalTabLayout = tabLayout
        }

        fun setViewPager(viewPager: ViewPager2?) = apply {
            this.viewPager = viewPager
        }

        fun setAnimationDuration(duration: Long) = apply {
            this.animationDuration = duration
        }

        fun setScrollToPosition(position: Float) = apply {
            this.scrollToPosition = position
        }

        fun setTabBackgroundColor(color: Int) = apply {
            this.tabBackgroundColor = color
        }

        fun setTabElevation(elevation: Float) = apply {
            this.tabElevation = elevation
        }

        fun setOnTabClickListener(listener: (position: Int) -> Unit) = apply {
            this.onTabClickListener = listener
        }

        // 新增导航栏适配方法
        fun setBottomMargin(margin: Int) = apply {
            this.bottomMargin = margin
        }

        fun setAutoDetectNavigationBar(autoDetect: Boolean) = apply {
            this.autoDetectNavigationBar = autoDetect
        }

        fun setMaxElevation(elevation: Float) = apply {
            this.maxElevation = elevation
        }

        fun setHorizontalMargin(margin: Int) = apply {
            this.horizontalMargin = margin
        }

        fun build(): StickyBottomTabPlugin {
            requireNotNull(coordinatorLayout) { "CoordinatorLayout is required" }
            requireNotNull(appBarLayout) { "AppBarLayout is required" }
            requireNotNull(originalTabLayout) { "Original TabLayout is required" }

            val config = Config(
                coordinatorLayout = coordinatorLayout!!,
                appBarLayout = appBarLayout!!,
                originalTabLayout = originalTabLayout!!,
                viewPager = viewPager,
                animationDuration = animationDuration,
                scrollToPosition = scrollToPosition,
                tabBackgroundColor = tabBackgroundColor,
                tabElevation = tabElevation,
                onTabClickListener = onTabClickListener,
                bottomMargin = bottomMargin,
                autoDetectNavigationBar = autoDetectNavigationBar,
                maxElevation = maxElevation,
                horizontalMargin = horizontalMargin
            )

            return StickyBottomTabPlugin(context, config)
        }
    }

    /**
     * 启用插件
     */
    fun enable() {
        if (isEnabled) return

        isEnabled = true

        // 检测导航栏并计算适配参数
        if (config.autoDetectNavigationBar) {
            detectNavigationBars()
        } else {
            actualBottomMargin = config.bottomMargin
        }

        setupScrollListener()

        // 启动时立即显示吸底Tab
        showInitialStickyBottomTab()

        android.util.Log.d("StickyBottomTabPlugin", "插件已启用，底部边距: ${actualBottomMargin}px")
    }

    /**
     * 禁用插件
     */
    fun disable() {
        if (!isEnabled) return

        isEnabled = false
        isInitialized = false  // 重置初始化标志
        removeScrollListener()
        hideStickyBottomTab()

        android.util.Log.d("StickyBottomTabPlugin", "插件已禁用")
    }

    /**
     * 检查插件是否已启用
     */
    fun isEnabled(): Boolean = isEnabled

    /**
     * 检查吸底Tab是否正在显示
     */
    fun isBottomTabVisible(): Boolean = isBottomTabVisible

    /**
     * 设置滚动监听器
     */
    private fun setupScrollListener() {
        offsetChangeListener = AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (isEnabled && !isAnimating && isInitialized) {
                checkBottomStickyTabVisibility(verticalOffset)
            }
        }
        config.appBarLayout.addOnOffsetChangedListener(offsetChangeListener)

        // 延迟启动滚动监听，确保初始吸底Tab已显示
        config.coordinatorLayout.post {
            config.coordinatorLayout.postDelayed({
                if (isEnabled) {
                    isInitialized = true  // 标记为已初始化
                    android.util.Log.d("StickyBottomTabPlugin", "滚动监听已启动")
                }
            }, 200) // 稍微延迟，确保初始吸底Tab已显示
        }
    }

    /**
     * 启动时显示初始吸底Tab
     */
    private fun showInitialStickyBottomTab() {
        config.coordinatorLayout.post {
            config.coordinatorLayout.postDelayed({
                if (isEnabled && !isAnimating && !isBottomTabVisible) {
                    android.util.Log.d("StickyBottomTabPlugin", "显示初始吸底Tab")
                    showStickyBottomTabWithoutAnimation()
                    isInitialized = true
                }
            }, 100) // 短暂延迟确保布局完成
        }
    }

    /**
     * 移除滚动监听器
     */
    private fun removeScrollListener() {
        offsetChangeListener?.let { listener ->
            config.appBarLayout.removeOnOffsetChangedListener(listener)
        }
        offsetChangeListener = null

        pageChangeCallback?.let { callback ->
            config.viewPager?.unregisterOnPageChangeCallback(callback)
        }
        pageChangeCallback = null
    }

    /**
     * 检测系统导航栏和状态栏高度
     */
    private fun detectNavigationBars() {
        try {
            // 检测状态栏高度
            statusBarHeight = getStatusBarHeight()

            // 检测导航栏高度
            navigationBarHeight = getNavigationBarHeight()

            // 计算实际的底部边距
            actualBottomMargin = config.bottomMargin + navigationBarHeight

            android.util.Log.d("StickyBottomTabPlugin",
                "导航栏检测完成 - 状态栏: ${statusBarHeight}px, 导航栏: ${navigationBarHeight}px, 实际底部边距: ${actualBottomMargin}px")

        } catch (e: Exception) {
            android.util.Log.w("StickyBottomTabPlugin", "导航栏检测失败，使用默认配置", e)
            actualBottomMargin = config.bottomMargin
        }
    }

    /**
     * 获取状态栏高度
     */
    private fun getStatusBarHeight(): Int {
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            context.resources.getDimensionPixelSize(resourceId)
        } else {
            // 默认状态栏高度（约25dp）
            (25 * context.resources.displayMetrics.density).toInt()
        }
    }

    /**
     * 获取导航栏高度
     */
    private fun getNavigationBarHeight(): Int {
        // 检查是否有导航栏
        if (!hasNavigationBar()) {
            return 100
        }

        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return if (resourceId > 0) {
            context.resources.getDimensionPixelSize(resourceId)
        } else {
            // 默认导航栏高度（约48dp）
            (48 * context.resources.displayMetrics.density).toInt()
        }
    }

    /**
     * 检查设备是否有导航栏
     */
    private fun hasNavigationBar(): Boolean {
        val activity = context as? android.app.Activity ?: return false

        return try {
            val windowManager = activity.windowManager
            val display = windowManager.defaultDisplay
            val realDisplayMetrics = android.util.DisplayMetrics()
            val displayMetrics = android.util.DisplayMetrics()

            display.getRealMetrics(realDisplayMetrics)
            display.getMetrics(displayMetrics)

            // 如果真实高度大于显示高度，说明有导航栏
            realDisplayMetrics.heightPixels > displayMetrics.heightPixels
        } catch (e: Exception) {
            android.util.Log.w("StickyBottomTabPlugin", "检测导航栏失败", e)
            false
        }
    }

    /**
     * 检测并更新吸底Tab的显示状态
     */
    private fun checkBottomStickyTabVisibility(verticalOffset: Int) {
        // 检查Tab是否已经完成布局
        if (config.originalTabLayout.height <= 0 || config.originalTabLayout.width <= 0) {
            android.util.Log.d("StickyBottomTabPlugin", "Tab布局尚未完成，跳过检测")
            return
        }

        // 获取原始Tab的屏幕位置信息
        val location = IntArray(2)
        config.originalTabLayout.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = config.originalTabLayout.height
        val screenHeight = context.resources.displayMetrics.heightPixels
        val tabBottomY = tabScreenY + tabHeight

        // 判断Tab的可见性状态
        val isTabVisible = tabScreenY < screenHeight && tabBottomY > 0
        val isTabScrolledOutOfBottom = tabScreenY >= screenHeight

        // 添加额外的安全检查：确保不是初始状态的异常位置
        val isValidPosition = tabScreenY > 0 && tabScreenY < screenHeight * 3 // 允许一定的容错范围

        android.util.Log.d("StickyBottomTabPlugin",
            "位置检测: verticalOffset=$verticalOffset, " +
                    "tabScreenY=$tabScreenY, tabBottomY=$tabBottomY, " +
                    "screenHeight=$screenHeight, " +
                    "isTabVisible=$isTabVisible, " +
                    "isTabScrolledOutOfBottom=$isTabScrolledOutOfBottom, " +
                    "isValidPosition=$isValidPosition")

        when {
            isTabVisible && isBottomTabVisible -> {
                // 原始Tab可见时，隐藏吸底Tab
                android.util.Log.d("StickyBottomTabPlugin", "原始Tab可见，隐藏吸底Tab")
                hideStickyBottomTab()
            }
            isTabScrolledOutOfBottom && !isBottomTabVisible && isValidPosition -> {
                // 原始Tab不可见时，显示吸底Tab（带动画）
                android.util.Log.d("StickyBottomTabPlugin", "原始Tab不可见，显示吸底Tab")
                showStickyBottomTab()
            }
        }
    }

    /**
     * 显示吸底Tab
     */
    private fun showStickyBottomTab() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始创建吸底Tab")
        isAnimating = true
        isBottomTabVisible = true

        // 创建吸底Tab容器
        stickyBottomTabContainer = createStickyTabContainer()
        val stickyTabLayout = createStickyTabLayout()
        stickyBottomTabContainer?.addView(stickyTabLayout)

        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            // 设置底部边距以避开导航栏
            bottomMargin = actualBottomMargin
            // 设置水平边距以避开侧边导航栏
            leftMargin = config.horizontalMargin
            rightMargin = config.horizontalMargin
        }

        config.coordinatorLayout.addView(stickyBottomTabContainer, layoutParams)

        // 入场动画：从底部滑入
        stickyBottomTabContainer?.let { container ->
            container.translationY = container.height.toFloat()
            container.animate()
                .translationY(0f)
                .setDuration(config.animationDuration)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        isAnimating = false
                        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab入场动画完成")
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        isAnimating = false
                    }
                })
                .start()
        }

        // 设置状态同步
        setupTabSync(stickyTabLayout)
    }

    /**
     * 显示吸底Tab（无动画，用于初始显示）
     */
    private fun showStickyBottomTabWithoutAnimation() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始创建初始吸底Tab（无动画）")
        isBottomTabVisible = true

        // 创建吸底Tab容器
        stickyBottomTabContainer = createStickyTabContainer()
        val stickyTabLayout = createStickyTabLayout()
        stickyBottomTabContainer?.addView(stickyTabLayout)

        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            // 设置底部边距以避开导航栏
            bottomMargin = actualBottomMargin
            // 设置水平边距以避开侧边导航栏
            leftMargin = config.horizontalMargin
            rightMargin = config.horizontalMargin
        }

        config.coordinatorLayout.addView(stickyBottomTabContainer, layoutParams)

        // 直接显示，无动画
        stickyBottomTabContainer?.translationY = 0f

        android.util.Log.d("StickyBottomTabPlugin", "初始吸底Tab显示完成")

        // 设置状态同步
//        setupTabSync(stickyTabLayout)
    }

    /**
     * 隐藏吸底Tab
     */
    private fun hideStickyBottomTab() {
        if (isAnimating || !isBottomTabVisible || stickyBottomTabContainer == null) return

        android.util.Log.d("StickyBottomTabPlugin", "开始隐藏吸底Tab")
        isAnimating = true
        isBottomTabVisible = false

        val containerToRemove = stickyBottomTabContainer
        stickyBottomTabContainer = null

        // 退场动画：向底部滑出
        containerToRemove?.animate()
            ?.translationY(containerToRemove.height.toFloat())
            ?.setDuration(config.animationDuration)
            ?.setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    try {
                        config.coordinatorLayout.removeView(containerToRemove)
                        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab退场动画完成")
                    } catch (e: Exception) {
                        android.util.Log.e("StickyBottomTabPlugin", "移除吸底Tab时发生异常", e)
                    }
                    isAnimating = false
                }
                override fun onAnimationCancel(animation: Animator) {
                    isAnimating = false
                }
            })
            ?.start()
    }

    /**
     * 创建吸底Tab容器
     */
    private fun createStickyTabContainer(): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            setBackgroundColor(config.tabBackgroundColor)
            // 使用动态elevation，确保在所有导航栏之上
            elevation = calculateOptimalElevation()
        }
    }

    /**
     * 计算最优的elevation值
     */
    private fun calculateOptimalElevation(): Float {
        // 检测现有导航栏的elevation
        val existingElevations = detectExistingNavigationElevations()

        // 计算一个合适的elevation值，确保吸底Tab在最上层
        val targetElevation = if (existingElevations.isNotEmpty()) {
            existingElevations.maxOrNull()?.plus(2f) ?: config.tabElevation
        } else {
            config.tabElevation
        }

        // 限制在最大值范围内
        val finalElevation = kotlin.math.min(targetElevation, config.maxElevation)

        android.util.Log.d("StickyBottomTabPlugin",
            "计算elevation - 现有导航栏: $existingElevations, 目标elevation: $finalElevation")

        return finalElevation
    }

    /**
     * 检测现有导航栏的elevation值
     */
    private fun detectExistingNavigationElevations(): List<Float> {
        val elevations = mutableListOf<Float>()

        try {
            // 检测CoordinatorLayout中的其他子视图
            for (i in 0 until config.coordinatorLayout.childCount) {
                val child = config.coordinatorLayout.getChildAt(i)
                if (child != stickyBottomTabContainer && child.elevation > 0) {
                    elevations.add(child.elevation)
                }
            }

            // 检测Activity中可能的底部导航栏
            val activity = context as? android.app.Activity
            activity?.findViewById<android.view.ViewGroup>(android.R.id.content)?.let { contentView ->
                detectNavigationViewsRecursively(contentView, elevations)
            }

        } catch (e: Exception) {
            android.util.Log.w("StickyBottomTabPlugin", "检测现有导航栏elevation失败", e)
        }

        return elevations
    }

    /**
     * 递归检测导航相关视图的elevation
     */
    private fun detectNavigationViewsRecursively(viewGroup: android.view.ViewGroup, elevations: MutableList<Float>) {
        for (i in 0 until viewGroup.childCount) {
            val child = viewGroup.getChildAt(i)

            // 检查是否为导航相关的视图
            if (isNavigationRelatedView(child) && child.elevation > 0) {
                elevations.add(child.elevation)
            }

            // 递归检查子视图
            if (child is android.view.ViewGroup) {
                detectNavigationViewsRecursively(child, elevations)
            }
        }
    }

    /**
     * 判断是否为导航相关的视图
     */
    private fun isNavigationRelatedView(view: android.view.View): Boolean {
        val className = view.javaClass.simpleName.lowercase()
        val resourceName = try {
            context.resources.getResourceEntryName(view.id).lowercase()
        } catch (e: Exception) {
            ""
        }

        // 检查常见的导航栏类名和ID
        return className.contains("navigation") ||
                className.contains("bottomnavigation") ||
                className.contains("tabbar") ||
                className.contains("toolbar") ||
                resourceName.contains("navigation") ||
                resourceName.contains("bottom") ||
                resourceName.contains("tab")
    }

    /**
     * 创建吸底Tab布局
     * 支持不同类型的TabLayout，特别优化CoIndicator支持
     */
    private fun createStickyTabLayout(): ViewGroup {
        return when (val originalTab = config.originalTabLayout) {
            is TabLayout -> {
                // 检查是否为CoIndicator
                if (originalTab is com.hbg.module.libkt.custom.indicator.CoIndicator) {
                    createCoIndicatorCopy(originalTab)
                } else {
                    createTabLayoutCopy(originalTab)
                }
            }
            else -> {
                // 对于自定义Tab组件，尝试通过反射或接口复制
                createGenericTabCopy(originalTab)
            }
        }
    }

    /**
     * 创建CoIndicator的副本
     * 专门为CoIndicator优化的复制方法
     */
    private fun createCoIndicatorCopy(originalCoIndicator: com.hbg.module.libkt.custom.indicator.CoIndicator): com.hbg.module.libkt.custom.indicator.CoIndicator {
        val stickyCoIndicator = com.hbg.module.libkt.custom.indicator.CoIndicator(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            // 复制原始CoIndicator的样式属性
            tabMode = originalCoIndicator.tabMode
            tabGravity = originalCoIndicator.tabGravity
        }

        // 使用tabTitles创建Tab（如果有的话）
        if (tabTitles.isNotEmpty()) {
            tabTitles.forEach { title ->
                if (title != null) {
                    stickyCoIndicator.addTab(title)
                }
            }
        } else {
            // 回退到复制原始Tab
            for (i in 0 until originalCoIndicator.tabCount) {
                val originalTab = originalCoIndicator.getTabAt(i)
                val newTab = stickyCoIndicator.newTab()
                newTab.text = originalTab?.text
                newTab.icon = originalTab?.icon
                stickyCoIndicator.addTab(newTab)
            }
        }

        // 同步选中状态
        val selectedPosition = originalCoIndicator.selectedTabPosition
        if (selectedPosition >= 0 && selectedPosition < stickyCoIndicator.tabCount) {
            stickyCoIndicator.getTabAt(selectedPosition)?.select()
        }

        // 设置点击监听器，使用CoIndicator的便捷方法
        stickyCoIndicator.setOnTabSelectedListener { position ->
            handleTabClick(position)
        }

        android.util.Log.d("StickyBottomTabPlugin", "创建CoIndicator副本完成，Tab数量: ${stickyCoIndicator.tabCount}")
        return stickyCoIndicator
    }

    /**
     * 创建TabLayout的副本
     */
    private fun createTabLayoutCopy(originalTabLayout: TabLayout): TabLayout {
        val stickyTabLayout = TabLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            // 复制原始TabLayout的样式属性
            tabMode = originalTabLayout.tabMode
            tabGravity = originalTabLayout.tabGravity
        }

        // 复制所有Tab
        for (i in 0 until originalTabLayout.tabCount) {
            val originalTab = originalTabLayout.getTabAt(i)
            val newTab = stickyTabLayout.newTab()
            newTab.text = originalTab?.text
            newTab.icon = originalTab?.icon
            stickyTabLayout.addTab(newTab)
        }

        // 同步选中状态
        val selectedPosition = originalTabLayout.selectedTabPosition
        if (selectedPosition >= 0 && selectedPosition < stickyTabLayout.tabCount) {
            stickyTabLayout.getTabAt(selectedPosition)?.select()
        }

        // 设置点击监听
        stickyTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    handleTabClick(it.position)
                }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab?.let {
                    handleTabClick(it.position)
                }
            }
        })

        return stickyTabLayout
    }

    /**
     * 创建通用Tab副本（用于自定义Tab组件）
     */
    private fun createGenericTabCopy(originalTab: ViewGroup): ViewGroup {
        // 这里可以根据具体的自定义Tab组件类型进行处理
        // 暂时返回一个简单的LinearLayout作为占位符
        return config.originalTabLayout
//        LinearLayout(context).apply {
//            layoutParams = LinearLayout.LayoutParams(
//                0,
//                LinearLayout.LayoutParams.WRAP_CONTENT,
//                1f
//            )
//            // 可以在这里添加更多的通用处理逻辑
//        }
    }

    /**
     * 处理Tab点击事件
     * 增强版本，支持触发原始CoIndicator的所有监听器
     */
    private fun handleTabClick(position: Int) {
        android.util.Log.d("StickyBottomTabPlugin", "吸底Tab点击: position=$position")

        // 1. 先执行自定义的点击监听器
        config.onTabClickListener?.invoke(position)

        // 2. 如果有CoIndicator适配器，触发原始监听器
        coIndicatorListenerAdapter?.let { adapter ->
            android.util.Log.d("StickyBottomTabPlugin", "触发原始CoIndicator监听器")
            adapter.triggerAllListeners(position)
        }

        // 3. 切换ViewPager页面（如果原始监听器没有处理）
        config.viewPager?.setCurrentItem(position, true)

        // 4. 点击吸底Tab时自动滚动到原始Tab位置
        performScrollToPosition()

        android.util.Log.d("StickyBottomTabPlugin", "Tab切换完成，开始滚动到原始Tab位置")
    }

    /**
     * 滚动到指定位置（默认屏幕1/3处）
     */
    protected open fun performScrollToPosition() {
        // 如果正在动画中，不执行滚动
        if (isAnimating) {
            android.util.Log.d("StickyBottomTabPlugin", "正在动画中，跳过滚动")
            return
        }

        config.coordinatorLayout.post {
            val screenHeight = context.resources.displayMetrics.heightPixels
            val targetY = (screenHeight * config.scrollToPosition).toInt()

            // 先完全展开AppBarLayout
            config.appBarLayout.setExpanded(true, false)

            // 等待布局完成后计算需要的滚动距离
            config.coordinatorLayout.post {
                val tabLocation = IntArray(2)
                config.originalTabLayout.getLocationOnScreen(tabLocation)
                val currentTabY = tabLocation[1]

                // 计算需要向下滚动的距离
                val scrollDistance = currentTabY - targetY

                if (scrollDistance > 0) {
                    // 设置动画标志，防止滚动监听器干扰
                    isAnimating = true

                    // 使用AppBarLayout的Behavior来精确控制滚动
                    val behavior = (config.appBarLayout.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior
                    if (behavior is AppBarLayout.Behavior) {
                        val totalScrollRange = config.appBarLayout.totalScrollRange
                        val targetOffset = kotlin.math.min(totalScrollRange, scrollDistance)

                        // 使用动画平滑滚动到目标位置
                        val animator = android.animation.ValueAnimator.ofInt(0, targetOffset)
                        animator.duration = 400
                        animator.interpolator = android.view.animation.DecelerateInterpolator()
                        animator.addUpdateListener { animation ->
                            val offset = animation.animatedValue as Int
                            behavior.topAndBottomOffset = -offset
                            config.appBarLayout.requestLayout()
                        }

                        // 添加动画结束监听器，滚动完成后隐藏吸底Tab
                        animator.addListener(object : android.animation.AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: android.animation.Animator) {
                                super.onAnimationEnd(animation)
                                isAnimating = false // 重置动画标志

                                // 滚动完成后，延迟一点时间再隐藏吸底Tab，确保原始Tab已经可见
                                config.coordinatorLayout.postDelayed({
                                    if (isBottomTabVisible) {
                                        hideStickyBottomTab()
                                        android.util.Log.d("StickyBottomTabPlugin", "滚动完成，吸底Tab已隐藏")
                                    }
                                }, 100) // 延迟100ms确保布局稳定
                            }

                            override fun onAnimationCancel(animation: android.animation.Animator) {
                                super.onAnimationCancel(animation)
                                isAnimating = false // 重置动画标志
                            }
                        })

                        animator.start()
                        android.util.Log.d("StickyBottomTabPlugin", "开始平滑滚动到目标位置")
                    } else {
                        isAnimating = false // 如果无法获取behavior，重置标志
                    }
                } else {
                    // 如果不需要滚动，直接隐藏吸底Tab
                    android.util.Log.d("StickyBottomTabPlugin", "无需滚动，直接隐藏吸底Tab")
                    if (isBottomTabVisible) {
                        hideStickyBottomTab()
                    }
                }

                android.util.Log.d("StickyBottomTabPlugin", "滚动到指定位置处理完成")
            }
        }
    }

    /**
     * 设置Tab状态同步
     */
    private fun setupTabSync(stickyTabLayout: ViewGroup) {
        config.viewPager?.let { viewPager ->
            pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // 同步吸底Tab的选中状态
                    if (isBottomTabVisible && stickyTabLayout is TabLayout) {
                        stickyTabLayout.getTabAt(position)?.select()
                    }
                }
            }
            viewPager.registerOnPageChangeCallback(pageChangeCallback!!)
        }
    }

    /**
     * 销毁插件，释放资源
     */
    fun destroy() {
        disable()
        coIndicatorListenerAdapter?.destroy()
        coIndicatorListenerAdapter = null
        originalCoIndicator = null
        android.util.Log.d("StickyBottomTabPlugin", "插件已销毁")
    }

    // ========== CoIndicatorAware 接口实现 ==========

    override fun setTabTitles(titles: List<String?>) {
        this.tabTitles = titles
        android.util.Log.d("StickyBottomTabPlugin", "设置Tab标题: ${titles.joinToString()}")
    }

    // ========== CoIndicatorListenerAware 接口实现 ==========

    override fun setCoIndicatorAdapter(coIndicator: com.hbg.module.libkt.custom.indicator.CoIndicator) {
        this.originalCoIndicator = coIndicator
        this.coIndicatorListenerAdapter = CoIndicatorListenerAdapter(coIndicator)
        android.util.Log.d("StickyBottomTabPlugin", "设置CoIndicator适配器")
    }

    /**
     * CoIndicator监听器适配器
     * 负责捕获和转发原始CoIndicator的各种监听器事件
     */
    private inner class CoIndicatorListenerAdapter(
        private val coIndicator: com.hbg.module.libkt.custom.indicator.CoIndicator
    ) {

        private var originalTabSelectedListeners: MutableList<TabLayout.OnTabSelectedListener> = mutableListOf()
        private var originalPageSelectListener: Any? = null
        private var indicatorHelperListener: Any? = null

        init {
            captureOriginalListeners()
        }

        /**
         * 捕获原始CoIndicator上的所有监听器
         */
        private fun captureOriginalListeners() {
            try {
                // 捕获TabLayout的OnTabSelectedListener
                captureTabSelectedListeners()

                // 捕获CoIndicator的PageSelectListener
                capturePageSelectListener()

                // 捕获IndicatorHelper设置的监听器
                captureIndicatorHelperListener()

                android.util.Log.d("StickyBottomTabPlugin", "已捕获原始CoIndicator监听器")
            } catch (e: Exception) {
                android.util.Log.e("StickyBottomTabPlugin", "捕获原始监听器失败", e)
            }
        }

        /**
         * 捕获TabLayout的OnTabSelectedListener
         */
        private fun captureTabSelectedListeners() {
            try {
                val listenersField = TabLayout::class.java.getDeclaredField("selectedListeners")
                listenersField.isAccessible = true
                @Suppress("UNCHECKED_CAST")
                val listeners = listenersField.get(coIndicator) as? ArrayList<TabLayout.OnTabSelectedListener>
                listeners?.let {
                    originalTabSelectedListeners.addAll(it)
                    android.util.Log.d("StickyBottomTabPlugin", "捕获到${it.size}个TabSelectedListener")
                }
            } catch (e: Exception) {
                android.util.Log.w("StickyBottomTabPlugin", "捕获TabSelectedListener失败", e)
            }
        }

        /**
         * 捕获CoIndicator的PageSelectListener
         */
        private fun capturePageSelectListener() {
            try {
                val listenerField = coIndicator.javaClass.getDeclaredField("listener")
                listenerField.isAccessible = true
                originalPageSelectListener = listenerField.get(coIndicator)
                if (originalPageSelectListener != null) {
                    android.util.Log.d("StickyBottomTabPlugin", "捕获到PageSelectListener")
                }
            } catch (e: Exception) {
                android.util.Log.w("StickyBottomTabPlugin", "捕获PageSelectListener失败", e)
            }
        }

        /**
         * 捕获IndicatorHelper设置的监听器
         */
        private fun captureIndicatorHelperListener() {
            try {
                val navigatorField = coIndicator.javaClass.getDeclaredField("navigator")
                navigatorField.isAccessible = true
                indicatorHelperListener = navigatorField.get(coIndicator)
                if (indicatorHelperListener != null) {
                    android.util.Log.d("StickyBottomTabPlugin", "捕获到IndicatorHelper监听器")
                }
            } catch (e: Exception) {
                android.util.Log.w("StickyBottomTabPlugin", "捕获IndicatorHelper监听器失败", e)
            }
        }

        /**
         * 触发所有捕获的监听器
         */
        fun triggerAllListeners(position: Int) {
            android.util.Log.d("StickyBottomTabPlugin", "触发所有监听器: position=$position")

            // 触发TabSelectedListener
            triggerTabSelectedListeners(position)

            // 触发PageSelectListener
            triggerPageSelectListener(position)

            // 触发IndicatorHelper监听器
            triggerIndicatorHelperListener(position)
        }

        /**
         * 触发TabSelectedListener
         */
        private fun triggerTabSelectedListeners(position: Int) {
            try {
                val tab = coIndicator.getTabAt(position)
                if (tab != null) {
                    originalTabSelectedListeners.forEach { listener ->
                        listener.onTabSelected(tab)
                    }
                    android.util.Log.d("StickyBottomTabPlugin", "已触发${originalTabSelectedListeners.size}个TabSelectedListener")
                }
            } catch (e: Exception) {
                android.util.Log.e("StickyBottomTabPlugin", "触发TabSelectedListener失败", e)
            }
        }

        /**
         * 触发PageSelectListener
         */
        private fun triggerPageSelectListener(position: Int) {
            try {
                originalPageSelectListener?.let { listener ->
                    val onSelectedMethod = listener.javaClass.getDeclaredMethod("onSelected", Int::class.java)
                    onSelectedMethod.invoke(listener, position)
                    android.util.Log.d("StickyBottomTabPlugin", "已触发PageSelectListener")
                }
            } catch (e: Exception) {
                android.util.Log.e("StickyBottomTabPlugin", "触发PageSelectListener失败", e)
            }
        }

        /**
         * 触发IndicatorHelper监听器
         */
        private fun triggerIndicatorHelperListener(position: Int) {
            try {
                indicatorHelperListener?.let { navigator ->
                    // 尝试调用CommonNavigator的onTabClick方法
                    val onTabClickMethod = navigator.javaClass.getDeclaredMethod("onTabClick", Int::class.java)
                    onTabClickMethod.isAccessible = true
                    onTabClickMethod.invoke(navigator, position)
                    android.util.Log.d("StickyBottomTabPlugin", "已触发IndicatorHelper监听器")
                }
            } catch (e: Exception) {
                android.util.Log.e("StickyBottomTabPlugin", "触发IndicatorHelper监听器失败", e)
            }
        }

        /**
         * 销毁适配器，释放资源
         */
        fun destroy() {
            originalTabSelectedListeners.clear()
            originalPageSelectListener = null
            indicatorHelperListener = null
        }
    }
}
