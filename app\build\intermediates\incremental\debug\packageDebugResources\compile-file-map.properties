#Tue Jul 29 22:38:41 CST 2025
com.ttv.demo.app-main-5\:/drawable/home_feed_tab_indicator.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\home_feed_tab_indicator.xml
com.ttv.demo.app-main-5\:/drawable/ic_home.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.ttv.demo.app-main-5\:/drawable/ic_launcher_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.ttv.demo.app-main-5\:/drawable/ic_launcher_foreground.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.ttv.demo.app-main-5\:/drawable/ic_market.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_market.xml
com.ttv.demo.app-main-5\:/drawable/ic_profile.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_profile.xml
com.ttv.demo.app-main-5\:/drawable/ic_trade.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_trade.xml
com.ttv.demo.app-main-5\:/drawable/ic_wallet.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_wallet.xml
com.ttv.demo.app-main-5\:/drawable/item_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\item_background.xml
com.ttv.demo.app-main-5\:/drawable/release_button_background.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\release_button_background.xml
com.ttv.demo.app-main-5\:/layout/activity_main.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.ttv.demo.app-main-5\:/layout/content_card.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\content_card.xml
com.ttv.demo.app-main-5\:/layout/floating_tab_layout.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\floating_tab_layout.xml
com.ttv.demo.app-main-5\:/layout/fragment_content.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_content.xml
com.ttv.demo.app-main-5\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.ttv.demo.app-main-5\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.ttv.demo.app-main-5\:/mipmap-hdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.ttv.demo.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.ttv.demo.app-main-5\:/mipmap-mdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.ttv.demo.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.ttv.demo.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.ttv.demo.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.ttv.demo.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.ttv.demo.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.ttv.demo.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.ttv.demo.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.ttv.demo.app-main-5\:/xml/backup_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.ttv.demo.app-main-5\:/xml/data_extraction_rules.xml=D\:\\AndroidStudioProjects\\demo\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
