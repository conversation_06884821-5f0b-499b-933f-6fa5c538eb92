package com.hbg.module.libkt.custom.sticky

import android.content.Context
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator


class CoIndicatorStickyBottomTabPlugin private constructor(
    context: Context,
    config: StickyBottomTabPlugin.Config
) : StickyBottomTabPlugin(context, config) {

    companion object {
        /**
         * 为CoIndicator创建插件的便捷方法
         */
        fun create(
            context: Context,
            coordinatorLayout: CoordinatorLayout,
            appBarLayout: AppBarLayout,
            coIndicator: CoIndicator,
            viewPager: ViewPager2,
            tabTitles: List<String>,
            animationDuration: Long = 300L,
            scrollToPosition: Float = 0.33f,
            bottomMargin: Int = 0,
            autoDetectNavigationBar: Boolean = true,
            horizontalMargin: Int = 0,
            maxElevation: Float = 16f
        ): CoIndicatorStickyBottomTabPlugin {

            val config = StickyBottomTabPlugin.Config(
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                originalTabLayout = coIndicator,
                viewPager = viewPager,
                animationDuration = animationDuration,
                scrollToPosition = scrollToPosition,
                tabBackgroundColor = 0xFFFFFFFF.toInt(),
                tabElevation = 8f,
                onTabClickListener = null,
                bottomMargin = bottomMargin,
                autoDetectNavigationBar = autoDetectNavigationBar,
                maxElevation = maxElevation,
                horizontalMargin = horizontalMargin
            )

            return CoIndicatorStickyBottomTabPlugin(context, config).apply {
                // 设置CoIndicator特有的配置
                setTabTitles(tabTitles)
            }
        }
    }

    private var tabTitles: List<String> = emptyList()

    /**
     * 设置Tab标题列表
     */
    override fun setTabTitles(titles: List<String?>) {
        this.tabTitles = titles.filterNotNull()
    }

    /**
     * 创建CoIndicator的副本
     */
    fun createCoIndicatorCopy(originalCoIndicator: CoIndicator): CoIndicator {
        val stickyIndicator = CoIndicator(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
        }

        // 复制Tab内容
        tabTitles.forEach { title ->
            stickyIndicator.addTab(title)
        }

        // 同步当前选中的Tab
        val currentItem = config.viewPager?.currentItem ?: 0
        if (currentItem < stickyIndicator.tabCount) {
            stickyIndicator.getTabAt(currentItem)?.select()
        }

        // 设置点击监听
        stickyIndicator.setOnTabSelectedListener { position ->
            handleCoIndicatorTabClick(position)
        }

        return stickyIndicator
    }

    /**
     * 处理CoIndicator的Tab点击
     */
    private fun handleCoIndicatorTabClick(position: Int) {
        android.util.Log.d("CoIndicatorStickyPlugin", "CoIndicator Tab点击: position=$position")

        // 切换ViewPager页面
        config.viewPager?.setCurrentItem(position, true)

        // 点击吸底Tab时自动滚动到原始Tab位置
        performScrollToPosition()

        android.util.Log.d("CoIndicatorStickyPlugin", "CoIndicator Tab切换完成，开始滚动到原始Tab位置")
    }

    /**
     * 设置CoIndicator的状态同步
     */
    fun setupCoIndicatorSync(stickyIndicator: CoIndicator) {
        config.viewPager?.let { viewPager ->
            val pageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // 同步吸底CoIndicator的选中状态
                    if (isBottomTabVisible() && position < stickyIndicator.tabCount) {
                        stickyIndicator.getTabAt(position)?.select()
                    }
                }
            }
            viewPager.registerOnPageChangeCallback(pageChangeCallback)
        }
    }
}
