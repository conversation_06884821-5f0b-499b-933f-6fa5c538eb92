package com.huobi.home.ui

import android.content.Context
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator

/**
 * 吸底Tab插件工厂类
 * 提供便捷的创建方法，简化集成过程
 */
object StickyBottomTabPluginFactory {

    /**
     * 为CoIndicator创建吸底Tab插件
     *
     * @param context 上下文
     * @param coordinatorLayout CoordinatorLayout容器
     * @param appBarLayout AppBarLayout
     * @param coIndicator 原始的CoIndicator
     * @param viewPager ViewPager2
     * @param tabTitles Tab标题列表
     * @param animationDuration 动画时长（毫秒），默认300ms
     * @param scrollToPosition 点击后滚动到屏幕的位置比例，默认0.33f（1/3处）
     * @param bottomMargin 底部边距，用于避开底部导航栏，默认0（自动检测）
     * @param autoDetectNavigationBar 是否自动检测导航栏，默认true
     * @param horizontalMargin 水平边距，用于避开侧边导航栏，默认0
     * @return 配置好的插件实例
     */
    fun createForCoIndicator(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabTitles: List<String?>,
        animationDuration: Long = 300L,
        scrollToPosition: Float = 0.33f,
        bottomMargin: Int = 100,
        autoDetectNavigationBar: Boolean = true,
        horizontalMargin: Int = 0,
        maxElevation: Float = 16f
    ): StickyBottomTabPlugin {

        return StickyBottomTabPlugin.Builder(context)
            .setCoordinatorLayout(coordinatorLayout)
            .setAppBarLayout(appBarLayout)
            .setOriginalTabLayout(coIndicator)
            .setViewPager(viewPager)
            .setAnimationDuration(animationDuration)
            .setScrollToPosition(scrollToPosition)
            .setBottomMargin(bottomMargin)
            .setAutoDetectNavigationBar(autoDetectNavigationBar)
            .setHorizontalMargin(horizontalMargin)
            .setMaxElevation(maxElevation)
            .setOnTabClickListener { position ->
                // CoIndicator特有的点击处理逻辑
                handleCoIndicatorClick(viewPager, position)
            }
            .build()
            .apply {
                // 设置CoIndicator特有的配置
                if (this is CoIndicatorAware) {
                    tabTitles?.let {
                        setTabTitles(tabTitles)

                    }
                }
            }
    }

    /**
     * 处理CoIndicator的点击事件
     */
    private fun handleCoIndicatorClick(viewPager: ViewPager2, position: Int) {
        viewPager.setCurrentItem(position, true)
    }

    /**
     * 标记接口，用于识别支持CoIndicator的插件
     */
    interface CoIndicatorAware {
        fun setTabTitles(titles: List<String?>)
    }
}
