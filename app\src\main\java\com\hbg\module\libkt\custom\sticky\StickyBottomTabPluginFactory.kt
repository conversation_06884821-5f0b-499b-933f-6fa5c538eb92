package com.huobi.home.ui

import android.content.Context
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.hbg.module.libkt.custom.indicator.CoIndicator

/**
 * 吸底Tab插件工厂类
 * 提供便捷的创建方法，简化集成过程
 */
object StickyBottomTabPluginFactory {

    /**
     * 为CoIndicator创建吸底Tab插件
     *
     * @param context 上下文
     * @param coordinatorLayout CoordinatorLayout容器
     * @param appBarLayout AppBarLayout
     * @param coIndicator 原始的CoIndicator
     * @param viewPager ViewPager2
     * @param tabTitles Tab标题列表
     * @param animationDuration 动画时长（毫秒），默认300ms
     * @param scrollToPosition 点击后滚动到屏幕的位置比例，默认0.33f（1/3处）
     * @param bottomMargin 底部边距，用于避开底部导航栏，默认0（自动检测）
     * @param autoDetectNavigationBar 是否自动检测导航栏，默认true
     * @param horizontalMargin 水平边距，用于避开侧边导航栏，默认0
     * @param preserveOriginalListeners 是否保留原始CoIndicator的监听器，默认true
     * @return 配置好的插件实例
     */
    fun createForCoIndicator(
        context: Context,
        coordinatorLayout: CoordinatorLayout,
        appBarLayout: AppBarLayout,
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        tabTitles: List<String?>,
        animationDuration: Long = 300L,
        scrollToPosition: Float = 0.33f,
        bottomMargin: Int = 100,
        autoDetectNavigationBar: Boolean = true,
        horizontalMargin: Int = 0,
        maxElevation: Float = 16f,
        preserveOriginalListeners: Boolean = true
    ): StickyBottomTabPlugin {

        return StickyBottomTabPlugin.Builder(context)
            .setCoordinatorLayout(coordinatorLayout)
            .setAppBarLayout(appBarLayout)
            .setOriginalTabLayout(coIndicator)
            .setViewPager(viewPager)
            .setAnimationDuration(animationDuration)
            .setScrollToPosition(scrollToPosition)
            .setBottomMargin(bottomMargin)
            .setAutoDetectNavigationBar(autoDetectNavigationBar)
            .setHorizontalMargin(horizontalMargin)
            .setMaxElevation(maxElevation)
            .setOnTabClickListener { position ->
                // CoIndicator特有的点击处理逻辑
                handleCoIndicatorClick(coIndicator, viewPager, position, preserveOriginalListeners)
            }
            .build()
            .apply {
                // 设置CoIndicator特有的配置
                if (this is CoIndicatorAware) {
                    tabTitles?.let {
                        setTabTitles(tabTitles)
                    }
                }

                // 如果需要保留原始监听器，设置CoIndicator适配器
                if (preserveOriginalListeners && this is CoIndicatorListenerAware) {
                    setCoIndicatorAdapter(coIndicator)
                }
            }
    }

    /**
     * 处理CoIndicator的点击事件
     */
    private fun handleCoIndicatorClick(
        coIndicator: CoIndicator,
        viewPager: ViewPager2,
        position: Int,
        preserveOriginalListeners: Boolean
    ) {
        android.util.Log.d("StickyBottomTabPluginFactory", "处理CoIndicator点击: position=$position, preserveListeners=$preserveOriginalListeners")

        if (preserveOriginalListeners) {
            // 触发原始CoIndicator的点击事件，保留所有客户自定义逻辑
            triggerOriginalCoIndicatorClick(coIndicator, position)
        } else {
            // 简单模式，只切换ViewPager
            viewPager.setCurrentItem(position, true)
        }
    }

    /**
     * 触发原始CoIndicator的点击事件
     * 模拟用户点击原始Tab，确保所有监听器都被触发
     */
    private fun triggerOriginalCoIndicatorClick(coIndicator: CoIndicator, position: Int) {
        try {
            // 方法1：直接选中Tab，触发TabLayout的监听器
            if (position >= 0 && position < coIndicator.tabCount) {
                val tab = coIndicator.getTabAt(position)
                tab?.select()
                android.util.Log.d("StickyBottomTabPluginFactory", "已触发原始CoIndicator Tab选中: position=$position")
            }

            // 方法2：尝试触发IndicatorHelper设置的监听器
            triggerIndicatorHelperListeners(coIndicator, position)

            // 方法3：触发CoIndicator的PageSelectListener
            triggerPageSelectListener(coIndicator, position)

        } catch (e: Exception) {
            android.util.Log.e("StickyBottomTabPluginFactory", "触发原始CoIndicator监听器失败", e)
        }
    }

    /**
     * 尝试触发IndicatorHelper设置的监听器
     */
    private fun triggerIndicatorHelperListeners(coIndicator: CoIndicator, position: Int) {
        try {
            // 通过反射获取IndicatorHelper设置的监听器
            val navigatorField = coIndicator.javaClass.getDeclaredField("navigator")
            navigatorField.isAccessible = true
            val navigator = navigatorField.get(coIndicator)

            if (navigator != null) {
                // 尝试获取CommonNavigator中的TabClickListener
                val commonNavigatorClass = Class.forName("com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator")
                val onTabClickMethod = commonNavigatorClass.getDeclaredMethod("onTabClick", Int::class.java)
                onTabClickMethod.isAccessible = true
                onTabClickMethod.invoke(navigator, position)

                android.util.Log.d("StickyBottomTabPluginFactory", "已触发IndicatorHelper监听器: position=$position")
            }
        } catch (e: Exception) {
            android.util.Log.w("StickyBottomTabPluginFactory", "触发IndicatorHelper监听器失败，尝试其他方法", e)
        }
    }

    /**
     * 触发CoIndicator的PageSelectListener
     */
    private fun triggerPageSelectListener(coIndicator: CoIndicator, position: Int) {
        try {
            val listenerField = coIndicator.javaClass.getDeclaredField("listener")
            listenerField.isAccessible = true
            val listener = listenerField.get(coIndicator)

            if (listener != null) {
                val onSelectedMethod = listener.javaClass.getDeclaredMethod("onSelected", Int::class.java)
                onSelectedMethod.invoke(listener, position)
                android.util.Log.d("StickyBottomTabPluginFactory", "已触发PageSelectListener: position=$position")
            }
        } catch (e: Exception) {
            android.util.Log.w("StickyBottomTabPluginFactory", "触发PageSelectListener失败", e)
        }
    }

    /**
     * 标记接口，用于识别支持CoIndicator的插件
     */
    interface CoIndicatorAware {
        fun setTabTitles(titles: List<String?>)
    }

    /**
     * 标记接口，用于识别支持CoIndicator监听器适配的插件
     */
    interface CoIndicatorListenerAware {
        fun setCoIndicatorAdapter(coIndicator: CoIndicator)
    }
}
