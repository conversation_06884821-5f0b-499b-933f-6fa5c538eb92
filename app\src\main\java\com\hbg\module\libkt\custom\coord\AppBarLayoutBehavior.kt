package com.hbg.module.libkt.custom.coord

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout
import com.ttv.demo.R

/**
 * 自定义AppBarLayout行为，实现Tab吸顶效果
 *
 * 核心逻辑：
 * 1. 检测带有app:layout_isSticky="true"属性的子视图
 * 2. 当其他内容滚动时，让sticky视图保持在AppBarLayout底部
 * 3. 实现真正的吸顶效果
 */
class AppBarLayoutBehavior : AppBarLayout.Behavior {

    constructor() : super()
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    override fun onLayoutChild(
        parent: CoordinatorLayout,
        abl: AppBarLayout,
        layoutDirection: Int
    ): Boolean {
        val result = super.onLayoutChild(parent, abl, layoutDirection)

        // 查找并处理sticky子视图
        processStickyViews(abl)

        return result
    }

    override fun onNestedScroll(
        coordinatorLayout: CoordinatorLayout,
        child: AppBarLayout,
        target: View,
        dxConsumed: Int,
        dyConsumed: Int,
        dxUnconsumed: Int,
        dyUnconsumed: Int,
        type: Int,
        consumed: IntArray
    ) {
        super.onNestedScroll(
            coordinatorLayout,
            child,
            target,
            dxConsumed,
            dyConsumed,
            dxUnconsumed,
            dyUnconsumed,
            type,
            consumed
        )

        // 处理sticky视图的位置调整
        handleStickyViewsOnScroll(child)
    }

    /**
     * 处理带有sticky属性的子视图
     */
    private fun processStickyViews(appBarLayout: AppBarLayout) {
        for (i in 0 until appBarLayout.childCount) {
            val child = appBarLayout.getChildAt(i)
            val layoutParams = child.layoutParams as? AppBarLayout.LayoutParams

            // 检查是否有sticky属性（这里简化处理，实际项目中可能需要从attrs中读取）
            if (layoutParams != null && isStickyView(child)) {
                // 设置sticky视图的特殊行为
                setupStickyBehavior(layoutParams)
                android.util.Log.d("AppBarBehavior", "找到sticky视图: ${child.id}")
            }
        }
    }

    /**
     * 判断是否为sticky视图
     * 这里简化处理，通过ID判断。实际项目中应该读取app:layout_isSticky属性
     */
    private fun isStickyView(view: View): Boolean {
        return view.id == R.id.home_feed_linear_tabLayout
    }

    /**
     * 设置sticky行为
     */
    private fun setupStickyBehavior(layoutParams: AppBarLayout.LayoutParams) {
        // 设置滚动标志，让视图可以滚动但在需要时保持可见
        layoutParams.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or
                                   AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS or
                                   AppBarLayout.LayoutParams.SCROLL_FLAG_SNAP
    }

    /**
     * 在滚动时处理sticky视图
     */
    private fun handleStickyViewsOnScroll(appBarLayout: AppBarLayout) {
        // 这里可以添加更复杂的sticky逻辑
        // 当前依赖于Material Design的原生行为
    }
}
