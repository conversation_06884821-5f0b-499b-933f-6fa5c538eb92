{"logs": [{"outputFile": "com.ttv.demo.app-mergeDebugResources-42:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\147e0183abcca07bfea377b96a799c57\\transformed\\appcompat-1.7.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,9616", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,9694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a6678e3bdc760601f94ecc08acf1a57f\\transformed\\material-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1044,1112,1204,1277,1340,1426,1488,1551,1616,1684,1747,1801,1933,1990,2052,2106,2180,2318,2399,2479,2582,2666,2746,2878,2963,3050,3191,3279,3358,3412,3465,3531,3603,3685,3756,3841,3913,3988,4059,4132,4238,4335,4409,4504,4601,4675,4760,4860,4913,4998,5066,5154,5244,5306,5370,5433,5500,5617,5729,5840,5951,6009,6066,6147,6232,6313", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "254,330,404,477,574,663,762,891,974,1039,1107,1199,1272,1335,1421,1483,1546,1611,1679,1742,1796,1928,1985,2047,2101,2175,2313,2394,2474,2577,2661,2741,2873,2958,3045,3186,3274,3353,3407,3460,3526,3598,3680,3751,3836,3908,3983,4054,4127,4233,4330,4404,4499,4596,4670,4755,4855,4908,4993,5061,5149,5239,5301,5365,5428,5495,5612,5724,5835,5946,6004,6061,6142,6227,6308,6388"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3005,3081,3155,3228,3325,4137,4236,4365,4448,4513,4581,4673,4746,4809,4895,4957,5020,5085,5153,5216,5270,5402,5459,5521,5575,5649,5787,5868,5948,6051,6135,6215,6347,6432,6519,6660,6748,6827,6881,6934,7000,7072,7154,7225,7310,7382,7457,7528,7601,7707,7804,7878,7973,8070,8144,8229,8329,8382,8467,8535,8623,8713,8775,8839,8902,8969,9086,9198,9309,9420,9478,9535,9699,9784,9865", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "304,3076,3150,3223,3320,3409,4231,4360,4443,4508,4576,4668,4741,4804,4890,4952,5015,5080,5148,5211,5265,5397,5454,5516,5570,5644,5782,5863,5943,6046,6130,6210,6342,6427,6514,6655,6743,6822,6876,6929,6995,7067,7149,7220,7305,7377,7452,7523,7596,7702,7799,7873,7968,8065,8139,8224,8324,8377,8462,8530,8618,8708,8770,8834,8897,8964,9081,9193,9304,9415,9473,9530,9611,9779,9860,9940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c07f183b10d45831b60fa2f6fedd0a58\\transformed\\core-1.16.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3508,3610,3707,3808,3915,4022,9945", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3503,3605,3702,3803,3910,4017,4132,10041"}}]}]}