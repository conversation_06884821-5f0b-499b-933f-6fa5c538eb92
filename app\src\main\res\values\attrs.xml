<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 主题属性定义 -->
    <attr name="Background_L1" format="color|reference" />
    <attr name="Button_Blue_Fill" format="color|reference" />
    <attr name="Text_L3" format="color|reference" />

    <!-- 自定义View属性 -->
    <declare-styleable name="RoundLinearLayout">
        <attr name="rv_backgroundColor" format="color|reference" />
        <attr name="rv_cornerRadius" format="dimension" />
        <attr name="rv_isRippleEnable" format="boolean" />
    </declare-styleable>
</resources>
