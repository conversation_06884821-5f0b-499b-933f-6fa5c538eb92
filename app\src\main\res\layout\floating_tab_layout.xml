<?xml version="1.0" encoding="utf-8"?>
<!-- 悬浮Tab布局 - 与原始Tab布局保持一致 -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/floating_tab_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF"
    android:elevation="8dp"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <!-- 使用标准的TabLayout替代自定义的CoIndicator -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/floating_tab_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/dimen_40"
        android:layout_weight="1"
        android:paddingLeft="@dimen/dimen_8"
        app:tabGravity="start"
        app:tabIndicatorColor="?attr/Button_Blue_Fill"
        app:tabIndicatorHeight="3dp"
        app:tabMaxWidth="@dimen/dimen_200"
        app:tabMinWidth="@dimen/dimen_20"
        app:tabMode="scrollable"
        app:tabPaddingEnd="@dimen/dimen_8"
        app:tabPaddingStart="@dimen/dimen_8"
        app:tabRippleColor="@android:color/transparent"
        app:tabSelectedTextColor="?attr/Button_Blue_Fill"
        app:tabTextColor="?attr/Text_L3" />

    <!-- 发布按钮 -->
    <LinearLayout
        android:id="@+id/floating_release_btn"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_20"
        android:layout_marginEnd="@dimen/dimen_16"
        android:background="@color/otc_fitter_item_unselect_color"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dimen_10"
        android:paddingEnd="@dimen/dimen_10"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dimen_20"
            android:gravity="center"
            android:text="@string/n_send_comment"
            android:textColor="?attr/Button_Blue_Fill"
            android:textSize="@dimen/global_text_size_12" />
    </LinearLayout>

</LinearLayout>
