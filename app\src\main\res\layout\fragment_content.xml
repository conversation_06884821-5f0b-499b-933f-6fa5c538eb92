<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:overScrollMode="never">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_tab_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="Tab 内容"
            android:textColor="#333333"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- 模拟内容卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="内容卡片标题"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="这是一段模拟的内容文本，用于演示滚动效果。当内容足够多时，用户向上滚动页面，Tab会吸附在顶部。点击Tab时，页面会自动滚动到Tab距离顶部1/3高度的位置。"
                    android:textColor="#666666"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 重复多个卡片来创建足够的内容 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="功能演示说明"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="1. 向上滚动页面，观察Tab吸底效果\n2. 点击不同的Tab，观察自动滚动到1/3高度位置\n3. 滚动过程中Tab会保持在顶部可见"
                    android:textColor="#666666"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 更多内容卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="技术实现要点"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="使用CoordinatorLayout + AppBarLayout实现吸底效果，通过layout_isSticky属性让Tab保持在顶部。点击Tab时计算目标位置并使用smoothScrollTo实现平滑滚动。"
                    android:textColor="#666666"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 填充更多内容以便测试滚动 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="#E8F5E8"
            android:gravity="center"
            android:text="更多内容区域\n\n继续向下滚动\n测试Tab吸底效果"
            android:textColor="#4CAF50"
            android:textSize="16sp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginTop="12dp"
            android:background="#E3F2FD"
            android:gravity="center"
            android:text="底部内容区域\n\n滚动到这里时\nTab应该已经吸附在顶部"
            android:textColor="#2196F3"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
