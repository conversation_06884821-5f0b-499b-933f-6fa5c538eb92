{"logs": [{"outputFile": "com.ttv.demo.app-mergeDebugResources-42:/values-zh/values-zh.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\649d75e0ce5c10d20468405142284225\\transformed\\jetified-refresh-header-classics-3.0.0-alpha\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,106,157,214,268,324,378,434", "endColumns": "50,50,56,53,55,53,55,60", "endOffsets": "101,152,209,263,319,373,429,490"}, "to": {"startLines": "11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "633,684,735,792,846,902,956,1012", "endColumns": "50,50,56,53,55,53,55,60", "endOffsets": "679,730,787,841,897,951,1007,1068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a365e4e86fff01972dad4ad6ed4d68ae\\transformed\\jetified-refresh-footer-classics-3.0.0-alpha\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,106,157,210,265,319,379", "endColumns": "50,50,52,54,53,59,53", "endOffsets": "101,152,205,260,314,374,428"}, "to": {"startLines": "4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "255,306,357,410,465,519,579", "endColumns": "50,50,52,54,53,59,53", "endOffsets": "301,352,405,460,514,574,628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\78f38ec7d50ebf0df55805951dfb8e98\\transformed\\jetified-refresh-layout-kernel-3.0.0-alpha\\res\\values-zh\\values-zh.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,91", "endOffsets": "158,250"}}]}]}