# Tab吸底效果演示Demo

这是一个基于Android CoordinatorLayout + AppBarLayout实现的Tab吸底效果演示项目。

## 功能特性

### 1. Tab吸底效果
- 当用户向上滚动页面内容时，Tab会自动吸附在屏幕顶部
- 使用`app:layout_isSticky="true"`属性实现吸底效果
- 基于Material Design的CoordinatorLayout和AppBarLayout

### 2. 点击Tab自动定位
- 点击任意Tab时，页面会自动滚动到Tab距离屏幕顶部1/3高度的位置
- 使用平滑滚动动画，提供良好的用户体验
- 支持多个Tab之间的快速切换

## 技术实现

### 核心组件
- `CoordinatorLayout`: 协调子View之间的交互行为
- `AppBarLayout`: 管理可折叠的应用栏区域
- `NestedScrollView`: 提供嵌套滚动支持
- `TabLayout`: Material Design风格的Tab导航
- `ViewPager2`: 现代化的页面滑动组件

### 关键属性
- `app:layout_scrollFlags="scroll"`: 让内容区域可以滚动
- `app:layout_isSticky="true"`: 让Tab区域吸附在顶部
- `app:layout_behavior="@string/appbar_scrolling_view_behavior"`: ViewPager2的滚动行为

### 滚动逻辑
```kotlin
private fun scrollToOneThirdPosition() {
    coordinatorLayout.post {
        val screenHeight = resources.displayMetrics.heightPixels
        val oneThirdHeight = screenHeight / 3
        val contentHeight = fluentContainer.height
        
        if (contentHeight > oneThirdHeight) {
            val targetScrollY = contentHeight - oneThirdHeight
            nestedScrollView.smoothScrollTo(0, targetScrollY)
        }
    }
}
```

## 项目结构

```
app/src/main/
├── java/com/ttv/demo/
│   ├── StickyBottomTabDemoActivity.kt # 主入口Activity，实现吸底Tab效果
│   ├── MainActivity.kt                # 备用Activity实现
│   └── ContentFragment.kt             # 内容Fragment
├── java/com/hbg/module/libkt/custom/coord/
│   └── AppBarLayoutBehavior.kt        # 自定义AppBar行为
├── res/
│   ├── layout/
│   │   ├── activity_main.xml          # 主布局文件
│   │   ├── fragment_content.xml       # Fragment布局
│   │   └── content_card.xml           # 内容卡片布局
│   ├── values/
│   │   ├── colors.xml                # 颜色资源
│   │   ├── dimens.xml                # 尺寸资源
│   │   └── strings.xml               # 字符串资源
│   └── ...
└── AndroidManifest.xml
```

## 使用说明

1. 启动应用后，可以看到多个Tab和丰富的内容
2. 向上滚动页面，观察Tab如何吸附在顶部
3. 点击不同的Tab，观察页面如何自动滚动到合适位置
4. 可以通过滑动ViewPager2来切换不同的内容页面

## 依赖库

- androidx.coordinatorlayout:coordinatorlayout
- com.google.android.material:material
- androidx.viewpager2:viewpager2
- androidx.fragment:fragment-ktx
- androidx.cardview:cardview

## 注意事项

- 确保内容高度足够，否则吸底效果可能不明显
- Tab点击滚动功能需要在布局完成后执行，使用了post()方法
- 支持Android API 24及以上版本
