)com.hbg.lib.widgets.LoadingRelativeLayout6com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior1com.hbg.module.libkt.custom.indicator.CoIndicatorCcom.hbg.module.libkt.custom.sticky.CoIndicatorStickyBottomTabPlugin!com.huobi.view.MyNestedScrollView*com.huobi.view.roundview.RoundLinearLayoutcom.ttv.demo.ContentFragment(com.ttv.demo.StickyBottomTabDemoActivity8com.ttv.demo.StickyBottomTabDemoActivity.TabPagerAdapter;com.ttv.demo.StickyBottomTabDemoActivity.TabFragmentAdapter5com.ttv.demo.StickyBottomTabDemoActivity.DemoFragment                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           