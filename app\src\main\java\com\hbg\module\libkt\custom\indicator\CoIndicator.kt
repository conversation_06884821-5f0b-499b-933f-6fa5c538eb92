package com.hbg.module.libkt.custom.indicator

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.hbg.module.libkt.custom.indicator.interfaces.IPagerNavigator

/**
 * 框架入口
 */
class CoIndicator : FrameLayout {

    var listener: PageSelectListener? = null
    var navigator: IPagerNavigator? = null
        private set
    var viewLine:View ? = null
    constructor(context: Context?) : super(context!!)
    constructor(context: Context?, attrs: AttributeSet?) : super(
        context!!, attrs
    )

    fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        navigator?.onPageScrolled(position, positionOffset, positionOffsetPixels)
    }

    fun onPageSelected(position: Int) {
        navigator?.onPageSelected(position)
        listener?.onSelected(position)
    }

    fun onPageScrollStateChanged(state: Int) {
        navigator?.onPageScrollStateChanged(state)
    }

    fun setNavigator(navigator: IPagerNavigator) {
        if (this.navigator === navigator) {
            return
        }
        if (this.navigator != null) {
            navigator.onDetachFromMagicIndicator()
        }
        this.navigator = navigator
        removeAllViews()
        if (this.navigator is View) {
            val lp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            addView(this.navigator as View?, lp)
            navigator.onAttachToMagicIndicator()
        }
    }

    fun setOnPageSelectListener(listener: PageSelectListener) {
        this.listener = listener
    }

    interface PageSelectListener {
        fun onSelected(position: Int)
    }
}