package com.hbg.module.libkt.custom.indicator

import android.content.Context
import android.util.AttributeSet
import com.google.android.material.tabs.TabLayout
import com.ttv.demo.R

/**
 * 自定义指示器，基于TabLayout扩展
 */
class CoIndicator @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    init {
        // 设置默认样式
        tabMode = MODE_SCROLLABLE
        tabGravity = GRAVITY_START
    }
    
    // 添加Tab的便捷方法
    fun addTab(title: String) {
        val tab = newTab()
        tab.text = title
        addTab(tab)
    }
    
    // 设置Tab选中监听器的便捷方法
    fun setOnTabSelectedListener(listener: (position: Int) -> Unit) {
        addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab?) {
                tab?.let { listener(it.position) }
            }
            
            override fun onTabUnselected(tab: Tab?) {}
            override fun onTabReselected(tab: Tab?) {
                tab?.let { listener(it.position) }
            }
        })
    }
}
