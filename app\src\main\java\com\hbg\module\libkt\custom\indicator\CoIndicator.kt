package com.hbg.module.libkt.custom.indicator

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.hbg.module.libkt.custom.indicator.interfaces.IPagerNavigator
import com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator

class CoIndicator : FrameLayout {

    var listener: PageSelectListener? = null
    var navigator: IPagerNavigator? = null
        private set
    var viewLine:View ? = null
    var tabMode: Int = 0
    var tabGravity: Int = 0

    constructor(context: Context?) : super(context!!)
    constructor(context: Context?, attrs: AttributeSet?) : super(
        context!!, attrs
    )

    private var currentSelectedPosition = 0
    private var totalTabCount = 0

    val tabCount: Int
        get() = totalTabCount

    val selectedTabPosition: Int
        get() = currentSelectedPosition

    fun getTabAt(position: Int): Tab? {
        return if (position >= 0 && position < tabCount) {
            Tab(position, navigator)
        } else {
            null
        }
    }

    fun addTab(title: String) {
        totalTabCount++
    }

    fun updateTabCount(count: Int) {
        totalTabCount = count
    }

    fun setOnTabSelectedListener(listener: (position: Int) -> Unit) {
        this.listener = object : PageSelectListener {
            override fun onSelected(position: Int) {
                listener(position)
            }
        }
    }

    fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        navigator?.onPageScrolled(position, positionOffset, positionOffsetPixels)
    }

    fun onPageSelected(position: Int) {
        currentSelectedPosition = position
        navigator?.onPageSelected(position)
        listener?.onSelected(position)
    }

    fun onPageScrollStateChanged(state: Int) {
        navigator?.onPageScrollStateChanged(state)
    }

    fun setNavigator(navigator: IPagerNavigator) {
        if (this.navigator === navigator) {
            return
        }
        if (this.navigator != null) {
            navigator.onDetachFromMagicIndicator()
        }
        this.navigator = navigator

        if (navigator is CommonNavigator) {
            totalTabCount = navigator.mAdapter?.getCount() ?: 0
        }

        removeAllViews()
        if (this.navigator is View) {
            val lp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
            addView(this.navigator as View?, lp)
            navigator.onAttachToMagicIndicator()
        }
    }

    fun setOnPageSelectListener(listener: PageSelectListener) {
        this.listener = listener
    }

    inner class Tab(
        val position: Int,
        private val navigator: IPagerNavigator?
    ) {
        var text: CharSequence? = null
        var icon: android.graphics.drawable.Drawable? = null

        fun select() {
            onPageSelected(position)
        }
    }

    interface PageSelectListener {
        fun onSelected(position: Int)
    }
}