package com.huobi.view

import android.content.Context
import android.util.AttributeSet
import androidx.core.widget.NestedScrollView

/**
 * 自定义NestedScrollView
 */
class MyNestedScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : NestedScrollView(context, attrs, defStyleAttr) {

    // 可以在这里添加自定义的滚动逻辑
    // 目前保持与标准NestedScrollView相同的行为

    override fun onScrollChanged(scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int) {
        super.onScrollChanged(scrollX, scrollY, oldScrollX, oldScrollY)
        android.util.Log.d("MyNestedScrollView", "📱 onScrollChanged: scrollY=$scrollY, oldScrollY=$oldScrollY")
    }

    override fun onNestedScroll(target: android.view.View, dxConsumed: Int, dyConsumed: Int, dxUnconsumed: Int, dyUnconsumed: Int, type: Int) {
        super.onNestedScroll(target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type)
        android.util.Log.d("MyNestedScrollView", "🔄 onNestedScroll: dyConsumed=$dyConsumed, dyUnconsumed=$dyUnconsumed, scrollY=$scrollY")
    }
}
